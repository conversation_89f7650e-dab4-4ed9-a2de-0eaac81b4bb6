using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Technoloway.Core.Entities;
using Technoloway.Core.Interfaces;
using Technoloway.Web.Models.Pricing;
using System.Text.Json;

namespace Technoloway.Web.Controllers;

public class PricingController : Controller
{
    private readonly IRepository<Category> _categoryRepository;
    private readonly IRepository<Service> _serviceRepository;
    private readonly IRepository<ServiceOption> _serviceOptionRepository;
    private readonly IRepository<ServiceOptionFeature> _featureRepository;
    private readonly IRepository<Quotation> _quotationRepository;
    private readonly IRepository<QuotationItem> _quotationItemRepository;
    private readonly IHeroSectionRepository _heroSectionRepository;

    public PricingController(
        IRepository<Category> categoryRepository,
        IRepository<Service> serviceRepository,
        IRepository<ServiceOption> serviceOptionRepository,
        IRepository<ServiceOptionFeature> featureRepository,
        IRepository<Quotation> quotationRepository,
        IRepository<QuotationItem> quotationItemRepository,
        IHeroSectionRepository heroSectionRepository)
    {
        _categoryRepository = categoryRepository;
        _serviceRepository = serviceRepository;
        _serviceOptionRepository = serviceOptionRepository;
        _featureRepository = featureRepository;
        _quotationRepository = quotationRepository;
        _quotationItemRepository = quotationItemRepository;
        _heroSectionRepository = heroSectionRepository;
    }

    public async Task<IActionResult> Index()
    {
        var heroSection = await _heroSectionRepository.GetActiveByPageWithSlidesAsync("Pricing");
        
        var categories = await _categoryRepository.GetAll()
            .Where(c => !c.IsDeleted && c.ParentID == null) // Only parent categories
            .Include(c => c.Services.Where(s => s.IsActive && !s.IsDeleted))
                .ThenInclude(s => s.ServiceOptions.Where(so => !so.IsDeleted))
                    .ThenInclude(so => so.Features.Where(f => !f.IsDeleted))
            .OrderBy(c => c.CategName)
            .ToListAsync();

        var categoryDtos = categories.Select(c => new CategoryPricingDto
        {
            Id = c.Id,
            Name = c.CategName,
            Description = c.CategDesc ?? "",
            StartingPrice = c.Services.Any() ? c.Services.Min(s => s.Price) : 0,
            ServiceCount = c.Services.Count,
            FeaturedFeature = GetFeaturedFeature(c),
            Services = c.Services.Select(s => new ServicePricingDto
            {
                Id = s.Id,
                Name = s.Name,
                Description = s.Description,
                BasePrice = s.Price,
                IconClass = s.IconClass,
                IsActive = s.IsActive,
                DisplayOrder = s.DisplayOrder,
                Options = s.ServiceOptions.Select(so => new ServiceOptionPricingDto
                {
                    Id = so.Id,
                    Name = so.OptName,
                    Description = so.OptDesc,
                    Cost = so.OptCost,
                    DiscountRate = so.OptDiscountRate,
                    Availability = so.OptAvailability,
                    Features = so.Features.Select(f => new FeaturePricingDto
                    {
                        Id = f.Id,
                        Name = f.FeatName,
                        Description = f.FeatDesc,
                        Cost = f.FeatCost,
                        DiscountRate = f.FeatDiscountRate,
                        Availability = f.FeatAvailability
                    }).ToList()
                }).ToList()
            }).OrderBy(s => s.DisplayOrder).ToList()
        }).ToList();

        var viewModel = new PricingPageViewModel
        {
            Categories = categoryDtos
        };

        ViewBag.HeroSection = heroSection;
        ViewBag.PageName = "Pricing";
        return View(viewModel);
    }

    [HttpGet]
    public async Task<IActionResult> GetCategoryDetails(int categoryId)
    {
        var category = await _categoryRepository.GetAll()
            .Where(c => c.Id == categoryId && !c.IsDeleted)
            .Include(c => c.Services.Where(s => s.IsActive && !s.IsDeleted))
                .ThenInclude(s => s.ServiceOptions.Where(so => !so.IsDeleted))
                    .ThenInclude(so => so.Features.Where(f => !f.IsDeleted))
            .FirstOrDefaultAsync();

        if (category == null)
        {
            return NotFound();
        }

        var categoryDto = new CategoryPricingDto
        {
            Id = category.Id,
            Name = category.CategName,
            Description = category.CategDesc ?? "",
            StartingPrice = category.Services.Any() ? category.Services.Min(s => s.Price) : 0,
            ServiceCount = category.Services.Count,
            Services = category.Services.Select(s => new ServicePricingDto
            {
                Id = s.Id,
                Name = s.Name,
                Description = s.Description,
                BasePrice = s.Price,
                IconClass = s.IconClass,
                IsActive = s.IsActive,
                DisplayOrder = s.DisplayOrder,
                Options = s.ServiceOptions.Select(so => new ServiceOptionPricingDto
                {
                    Id = so.Id,
                    Name = so.OptName,
                    Description = so.OptDesc,
                    Cost = so.OptCost,
                    DiscountRate = so.OptDiscountRate,
                    Availability = so.OptAvailability,
                    Features = so.Features.Select(f => new FeaturePricingDto
                    {
                        Id = f.Id,
                        Name = f.FeatName,
                        Description = f.FeatDesc,
                        Cost = f.FeatCost,
                        DiscountRate = f.FeatDiscountRate,
                        Availability = f.FeatAvailability
                    }).ToList()
                }).ToList()
            }).OrderBy(s => s.DisplayOrder).ToList()
        };

        return Json(categoryDto);
    }

    [HttpPost]
    public async Task<IActionResult> SubmitQuotation([FromBody] QuotationRequestDto request)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return Json(new QuotationResponseDto
                {
                    Success = false,
                    Message = "Please fill in all required fields correctly."
                });
            }

            // Generate quotation number
            var quotationNumber = await GenerateQuotationNumber();

            // Create quotation
            var quotation = new Quotation
            {
                QuotationNumber = quotationNumber,
                CustomerName = request.CustomerName,
                Email = request.Email,
                Phone = request.Phone,
                Company = request.Company,
                TotalAmount = request.TotalAmount,
                AdditionalNotes = request.AdditionalNotes,
                Status = "Pending",
                CreatedAt = DateTime.UtcNow
            };

            await _quotationRepository.AddAsync(quotation);

            // Create quotation items
            foreach (var item in request.Items)
            {
                var quotationItem = new QuotationItem
                {
                    QuotationId = quotation.Id,
                    CategoryId = item.CategoryId,
                    CategoryName = item.CategoryName,
                    ServiceId = item.ServiceId,
                    ServiceName = item.ServiceName,
                    ServicePrice = item.ServicePrice,
                    ItemTotal = item.ItemTotal,
                    SelectedOptionsJson = JsonSerializer.Serialize(item.SelectedOptions),
                    SelectedFeaturesJson = JsonSerializer.Serialize(item.SelectedFeatures),
                    CreatedAt = DateTime.UtcNow
                };

                await _quotationItemRepository.AddAsync(quotationItem);
            }

            // TODO: Send email notification to admin
            // TODO: Send confirmation email to customer

            return Json(new QuotationResponseDto
            {
                Success = true,
                Message = "Your quotation request has been submitted successfully! We'll get back to you within 24 hours.",
                QuotationId = quotationNumber
            });
        }
        catch (Exception ex)
        {
            return Json(new QuotationResponseDto
            {
                Success = false,
                Message = "An error occurred while submitting your request. Please try again."
            });
        }
    }

    private async Task<string> GenerateQuotationNumber()
    {
        var today = DateTime.UtcNow;
        var prefix = $"QT{today:yyyyMM}";

        var lastQuotation = await _quotationRepository.GetAll()
            .Where(q => q.QuotationNumber.StartsWith(prefix))
            .OrderByDescending(q => q.QuotationNumber)
            .FirstOrDefaultAsync();

        int nextNumber = 1;
        if (lastQuotation != null)
        {
            var lastNumberStr = lastQuotation.QuotationNumber.Substring(prefix.Length);
            if (int.TryParse(lastNumberStr, out int lastNumber))
            {
                nextNumber = lastNumber + 1;
            }
        }

        return $"{prefix}{nextNumber:D4}";
    }

    private string GetFeaturedFeature(Category category)
    {
        // Get the most expensive feature as the "featured" one
        var allFeatures = category.Services
            .SelectMany(s => s.ServiceOptions)
            .SelectMany(so => so.Features)
            .Where(f => f.FeatCost.HasValue)
            .OrderByDescending(f => f.FeatCost)
            .FirstOrDefault();

        return allFeatures?.FeatName ?? "Professional Service";
    }
}
