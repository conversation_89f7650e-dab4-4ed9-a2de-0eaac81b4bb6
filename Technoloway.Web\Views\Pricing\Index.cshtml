@model Technoloway.Web.Models.Pricing.PricingPageViewModel
@{
    ViewData["Title"] = Model.PageTitle;
    ViewData["Description"] = Model.PageDescription;
}

<!-- Hero Section -->
@if (ViewBag.HeroSection != null)
{
    @await Html.PartialAsync("_HeroSection", (object)ViewBag.HeroSection)
}

<!-- Pricing Section -->
<section class="pricing-section py-5">
    <div class="container">
        <!-- Section Header -->
        <div class="row justify-content-center mb-5">
            <div class="col-lg-8 text-center">
                <h2 class="display-4 fw-bold text-primary mb-3">@Model.PageTitle</h2>
                <p class="lead text-muted">@Model.PageDescription</p>
            </div>
        </div>

        <!-- Category Cards Grid -->
        <div class="row g-4" id="categoryGrid">
            @foreach (var category in Model.Categories)
            {
                <div class="col-lg-4 col-md-6">
                    <div class="pricing-card h-100" data-category-id="@category.Id">
                        <div class="card border-0 shadow-lg h-100 pricing-card-inner">
                            <div class="card-header bg-primary text-white text-center py-4">
                                <div class="pricing-icon mb-3">
                                    <i class="fas fa-folder-open fa-3x"></i>
                                </div>
                                <h4 class="card-title mb-2">@category.Name</h4>
                                <p class="card-text opacity-75">@category.Description</p>
                            </div>
                            <div class="card-body p-4">
                                <div class="pricing-info mb-4">
                                    <div class="price-display text-center mb-3">
                                        <span class="price-label text-muted small">Starting from</span>
                                        <div class="price-amount">
                                            <span class="currency">$</span>
                                            <span class="amount">@category.StartingPrice.ToString("N0")</span>
                                        </div>
                                    </div>
                                    <div class="featured-feature text-center">
                                        <span class="badge bg-success">@category.FeaturedFeature</span>
                                    </div>
                                </div>
                                <div class="service-stats mb-4">
                                    <div class="stat-item d-flex justify-content-between">
                                        <span class="text-muted">Services Available:</span>
                                        <span class="fw-bold">@category.ServiceCount</span>
                                    </div>
                                </div>
                            </div>
                            <div class="card-footer bg-transparent p-4">
                                <button class="btn btn-primary btn-lg w-100 customize-btn" 
                                        data-category-id="@category.Id"
                                        data-category-name="@category.Name">
                                    <i class="fas fa-cogs me-2"></i>Customize & Quote
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            }
        </div>

        <!-- Features Section -->
        <div class="row mt-5">
            <div class="col-12">
                <div class="features-section bg-light rounded-3 p-5">
                    <div class="row text-center">
                        <div class="col-md-3 mb-4">
                            <div class="feature-item">
                                <i class="fas fa-calculator fa-3x text-primary mb-3"></i>
                                <h5>Real-time Pricing</h5>
                                <p class="text-muted">Get instant price calculations as you customize your service package</p>
                            </div>
                        </div>
                        <div class="col-md-3 mb-4">
                            <div class="feature-item">
                                <i class="fas fa-layer-group fa-3x text-success mb-3"></i>
                                <h5>Flexible Options</h5>
                                <p class="text-muted">Mix and match services, options, and features to fit your exact needs</p>
                            </div>
                        </div>
                        <div class="col-md-3 mb-4">
                            <div class="feature-item">
                                <i class="fas fa-file-invoice fa-3x text-warning mb-3"></i>
                                <h5>Instant Quotes</h5>
                                <p class="text-muted">Generate and submit quotation requests with detailed breakdowns</p>
                            </div>
                        </div>
                        <div class="col-md-3 mb-4">
                            <div class="feature-item">
                                <i class="fas fa-headset fa-3x text-info mb-3"></i>
                                <h5>Expert Support</h5>
                                <p class="text-muted">Get personalized assistance from our team within 24 hours</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Customization Modal -->
<div class="modal fade" id="customizationModal" tabindex="-1" aria-labelledby="customizationModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="customizationModalLabel">
                    <i class="fas fa-cogs me-2"></i>Customize Your Service Package
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body p-0">
                <div id="customizationContent">
                    <!-- Dynamic content will be loaded here -->
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quotation Modal -->
<div class="modal fade" id="quotationModal" tabindex="-1" aria-labelledby="quotationModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title" id="quotationModalLabel">
                    <i class="fas fa-file-invoice me-2"></i>Request Quotation
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="quotationContent">
                    <!-- Quotation form will be loaded here -->
                </div>
            </div>
        </div>
    </div>
</div>

@section Styles {
    <link href="~/css/pricing.css" rel="stylesheet" />
}

@section Scripts {
    <script src="~/js/pricing.js"></script>
}
