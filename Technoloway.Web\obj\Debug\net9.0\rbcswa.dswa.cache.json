{"GlobalPropertiesHash": "2ilJ2M8+ZdH0swl4cXFj9Ji8kay0R08ISE/fEc+OL0o=", "FingerprintPatternsHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["cytOH4XOCw88QmvZ3rZ9QzZlARLl1h3SC6w8xKQ8rVI=", "pbXKV3BYn4T0Hr7Wa2RTCfntFfBT1EJFzb6827ENmbc=", "D/+4B0W1nGyytL0nMRuFE9yRNhI0w0lQUDafpkEm4Ro=", "mKbSeUxxIkKpPRpz91/MdXVQmhSaoqSXkDlpzJUQ9WY=", "eNJUA11cvoDo+SbhT3qMMjWe8Q/lMvK8Nq/mqdvfPwI=", "Q6R/xxonomUtjOTsuYKsF+lo+iaVjpsbXfa1GGip838=", "e7RoElni0hnzSPYGVLD6uYuZU4z2quhSdRdZt6lR8mo=", "ATuiQeOSST4uy+Rgv9juelz6D+XVzLS85JOxVOJOGMw=", "VMgHcv6zzch11rZsnnVAF1pbsvkrt9MljnImyDO/p5A=", "NGfJxi8LQl4c26Go1UYNVVMt9EE2caeenATttfiIyh0=", "Lzs8jQfPjeESos0++T/Nb3JJo3haCkiMHkATluJFV4E=", "S+9Nml4mBePRWNNNCJuZS/xVdgvxaVsMtHq9/wEF/E0=", "9EWOHXGv3ZTm4AHt614+rcGvbOOoEZrSIaY7EwR0mxI=", "M7TglD3Eh68bC+QujmR11I4ghF7GTSompv1RfttUoZM=", "+tCyegHcFgwz89CcrPfpDtogPohgrXSQb0QwCBI6+BQ=", "CQUUkKfn/sTC/35J1AERDdnPMsF35W0QAsq0JT3LW1w=", "MxmMakwub83ciNcjLuHsTmnEPNCslZsNlqm/gDZ0ubA=", "42gjOT0m3lcvsshKlRlV2+lZVb+5zwxK2AysCW+v4dU=", "kl61azyY73xyaZIj2Kp5mYdZVcefqEBL7LZ/aXVq8+k=", "rhpDVIiPliiESPpVdCjTDhf2/tuME+XDGZnVSM0y/qM=", "zyaFMlTE5o6l7Gkk3B3xhjlJ6OwT9OXXgUop0g1P7mk=", "9dbAOHKxe2iiG+NgN94dnyvV+ZZpCjTRvowQolv59bw=", "mx+MhB2bYtVz7NvbqUqXim4oeoCldk5BAGFt56Ru41w=", "Qrz5iNF6ncjMG2n4cEQODSXIshP9OFjiDSNFIjuX0IQ=", "sa9pSLP0VdxTIKkeOBd1hPe5LJUDUTv9XTDToHxJ9/U=", "kM6a0/ZvhhumhEv8DUCdltXOffLyqiknizJWQP7UoKo=", "BFZfscgvfWaqSshWb7MZYTFfubKlYsYySefOMwW03bQ=", "iwtDpdTkZMZooLfcN3GLSsHjyqLBPOLXUzNyhGdlc4g=", "1vR+/da9hcCOgZRZf+7Usx16nPgX5C09IsGetSwSRM4=", "Q3bUxDsx/pC7pfm1kqWJ5ITjJIo9RG+KCVa7dyppyc4=", "lmZrn5b3j3gW7X7rYSLQvcwpeAq5ik2N+ebUeMcfU6Q=", "UGN/H4sbD3otN9eANvWNw5cVmGnNq+rtGcYtAqxASvE=", "zYPPeJOppzSD79W//0jFaIId8qVaqzaFl1MVlSfrI6c=", "A9Pk9fHu1OslWAXl23cdqpOttiPtnYeicnQfLfzK5eU=", "2lGBc9CecGSTvK7Z8elhNQ87oXnJgyq/WWI9U2rLlV8=", "gRWw/WFwEw4kF5VeVQqeuKbwJxm75ftMOBudjwHH+y4=", "ofEunXHsBsh20P9oMuNPnWANtO95/2jofQTOWq2UwG8=", "UPIs5LZcGEsUkY9Yj8Bs3XBqFOfu+nxo0Zq9WQY7fGM=", "5pFSwggwTDmhNyeQsWxkrMY982jZstzThXc/CAMQNq8=", "tEswAaqGZn2vkLCJVW45pPf+AY1ZDsGe/gZ+EjQthmc=", "B43Ktv2UMdVnTUqqKxDnBN/yg/MLRDpW5DnKAUGFKiI=", "I9PAmvpQ8QRDI2IqZWwidpx5ft+2i2tlQlM/seKKcXE=", "ZJF9Rl8WmjKIajXqsabhQiuPRGEbvvibFwxtpAwpemc=", "4vH/mvk3IUUZTovQgHpb+YhX5BxEzHtbzujgGRSgpJI=", "Rkm743oyFMN7GYtgWKeGfefnu26Km88hSvBC8hizk7g=", "ZAEWG40iNeq6aQ64kHyrYsOE/PUDIfmgoBaq2nHTcFA=", "JqVSA4Mf7X4Rm3VzcalvR3nzPfK8I4XVddiJyE9joec=", "wV9RdeiZOiMs92ZOXLHmb5OtNIHP93UyXBnBCHwHkk4=", "PbEAW3qfWfKnrCs7RgUD0UtgBkNM8QljIKYGRgl7C0Q=", "1BzlWh0mbvN/IRJkHQ3+G1ezjY7f60k6VehwBvKWbfo=", "VBXitRYxCnGFHziPfNkH91+9TBmVVcyvwLun8TgA848=", "XGgCPNxMoFG7oe1IFDw2jhMlPedz5Oatsehe1sDUbxc=", "/NpNAKcmKKUAep7crbT4zSOXW7JORIasI0LpbwZbeVc=", "vnCtfXTzl9vvqEDWQOQgNfvSxC8oR6Bac/W3w8sXuI8=", "pBNOQNUk8jxcnKKg3PfcuDOG/Q+mL8E2NefaLOhP2Io=", "plCanDehMmJGMeuCDteRCc/zPHvdWQPlxbvr7Jj8SRc=", "1Pn8pFbUro+xL91NL4wzh/2qnt0reCKDXJ/yRamWTLc=", "+qIlkYC4lAqcBUUNv/oZXbCfjCbNJtNYjIfzB3Kbx4c=", "/khUTZCvMrK6s/7VUoUVN4YIhXsxbtH0+4rYO7QFyq4=", "P2DVS3c4hG7X9oHa5LBYwQ1AyxqQCSeHVsSJUwAhbbY=", "XLq0zVlGnWRV2REMRk5aQk/yGFnQh2dRIE2353zd3vY=", "a44tOHjSfYWxSrslxrYAFYzqIQ8FloWWxi+TG7/H/wI=", "7nxbKsuMLIAVqPbNrkRXOJavUHK8FFeCCzRjdKMexiw=", "8XsC6R7RHd8zQQ++WL+usGSrw8H6pdAuVlq0KLKymTQ=", "/nwh7/thaJjT8q3l43CG0wlZaQ6/bvRtnDJbvk+ATVo=", "rn40E6w3a+Na8VQ3GJoJPcxfRfRhyZPc/BaJu5nknYU=", "UznxwcqKqXsHXTtyae4VZcoeE1FrpAuBf7HuWdaVaZ0=", "wExBWOOxrg5t9aEXf1ZzQMWjkMRv51oOMFs8wMbt4Bo=", "NNMmpM8eBmKQPHLhkd77xIl4KalDW6jHhgD04kwynAE=", "33l1R+EHUcv5APa2LHJLcbte4Os+SUwSILcHJwKTETM=", "nK6IIHAJif/Z8yA/HqdtOxK4G2kc97YMYzOgGtlbGbY=", "7UX9aJsexFoTdCXBuqw2gbj68WiaqD+78a+150ypxsA=", "AAn7eMsqd/LNifcl2scpGtdZOzLjwK5PF/oMXa3vlmE=", "opB5dL33tt86V6637fzKZmN2o3UZTF06a+QcWLx5ON0=", "Gbgum7kXLfXdV5z+cj7/dySsVFUtc4CwyQ6J75WC0h0=", "3WIw4uPpvTmaS1d3oIfOsx+hNGOsdXar/UeYWGnDSBM=", "BOZfW4WaRqtKMQxeMpERsAOXTXfo0fcso/3BTnmas+g=", "VWwTNGkIA66FiDnJIq6EeMDO5ox0eQZ4dNVP3QcydMg=", "u4XVIG3gjh8J+BJhP2Pg9BLpVZp6RN3q4LbooS1o41Y=", "gwSH2Au48FBsNuSu1t6vLnVYxHL3N5GuTi2pd9Tfukg=", "qU+YfI17rcIlTN8GzGfdiqqwxX7gX4BpHhAvKSNP1nk=", "ntFKz4RFoavLLCPkjliEs3XsAbx7qTdKPXiDiAvjg5Q=", "PDImZ5WmEdH9YputCjmgYLvUyDoomCFTK9b1grocy6c=", "T5uUg7WhmFw6kMdEAwMbgoANEOrYg4S9rWa8KlYU7fc=", "yQ+x+81YhVdS5tax5t2dLOopJbaNdV521XR0WlqkhyY=", "Hz8w884iaaiy9Z9yQ/NbUCZbCwYUpsGnNt6AAAKwHKI=", "/bk67e2OOFNnGx684kcwfWYRSw8E2kxTP/107cbZsX8=", "+XyPqLyLYIchyZ+vdPnw/rR+PQxzqmOpqmQWL5jWMfE=", "PAjnCt3TW2WnAXz7wt1S3EHZqmMTZLj8feLf8iocQBw=", "PMiNlXHNQB8u9JwoHXMeLdT7BWX+0B6p31mcaA0jUpE=", "N8dyKqB4Ae5tNQ1pUh829C9FgnMDltoYTf5mEsClAjI=", "E06fGQW4Vq6bPJNHBUiWY09jyRVJpSxN/WF04gTzf0o=", "Ox7B5wGlJ3EgU6umyvB6uu2h5JPXhAsutRNxHc+wTt8=", "ppfk9kvXAqKcNHDoE6IYKYMSn+QtUbZao1XX2pEya74=", "H4oLrQEqHtUHmv2T8cn5Q4wbv2rd5vCuBWKiOSxp8pU=", "WpJQE2rB62eUkbJlkdrPEjiPl+mQr+MvnqIEeJmVTVc=", "AbXE1HT4SUEU9/j6s3ceoSHGYvq4+62F7bT8pt2GfFg=", "ltPeHrVPM10dRJRrF44Ia0khdmFOi9QpAajyXWmgA0k=", "U9OiU+vd7W+05D/LgXAH1GYTTwT6bDNSq12/0CIxNG4=", "fz/EiH0X7kf07aQLHnvKUBd5lJKfuLBjEcUWjnos81U=", "LRH2iUaR6G1JcsWxW16/9kiuVJWJ3Cz1dvPgyO8yk3Q=", "FnWAMK9iX1pmHx6Kwe+uZnwClZRKbKL8r7m7mXZMGZ0=", "nQdNtii15HRFT8JHDAMz3Bc+tEZ4j/wPYVsFBNHn4o8=", "hoGvUD4Iu0dYsUxwUWySIV3fjBLxcZsPfxMSHqZNgnk=", "JRgRqsAf3mKXt8zFI/kOlz/Iz4pq7D8eOPuIaQUoN2s=", "vUtnezYu9dSodCjWny3Zlj4ouMWaeKOAJMg2+/XtJZk=", "hd9bZV6HJSdWSNtxWlNnUX76knJ/oXlIm1fclagYXCg=", "aH3mwbwK3tou8DsB7geLB94Gh52MmKzWjfn+Nt7+6UE=", "03Y1uYlpIF0dE5wlN7KTPIBZ8X8tP6mj3FMFuj9UpX8=", "Vj+t6b2JPfXRr/KxmL9qBBocemK9hYpRlwwKC/lfGCo=", "9dgs0xu1QudaS5ZnB9EJcj+xRBjBZLV+c5WvVaPW9Po=", "FiyyEyK1PxCpcVZU7pJyMAj0zI9SuQzl2mv1G9s4mro=", "usAnrxcrNp7pDsEmW5gjq0xfLlgE9RPeSolSYxDxj+E=", "vuJCokmXD42cjDScmmbPbC9ClOOZzx5P7n+Kgad0hbE=", "pe/cDNF1WDWScH7gJcyYOvlQtTqbSdd4V7699vVB5y4=", "9LEwbr4tI/kVHXRlPXLZnHXGABLOV0na/YelRox3Fr4=", "dhsivAhwOYqqBZZZB+UFgscHtGRNzPPHxdfSKgaFM4Q=", "QN29E3Jqs88jSARuG55Uuj+aobRbex8rEY5w9Ol9HmU=", "2QS/cEzFHI2H2Rrmz6oOdQ8m2IHKKhEu4abetH/ozoI=", "3osGeOGKwLf8zkynplGJ3v/CUPcAr9Rak9Y3UjA5rRg=", "Q1w6o7sUNQ0sCKd29WO5JFg3v3da3TkHt5DXjnx6UlA=", "E0MVCGJhEglNiMGXmHDapT7bGZgL5RJHoKKY+4vCTkQ=", "dsfXAOpjcGT1wRwJkOTb3r59KMSn5h2QNCsT7nLTOI4=", "B5oes6nAdG6qG5ufNDLwnIBvV6Q9SZOuorKPa3pHKtM=", "+9izxIDeCJtml7RBOZNllIWIVDQfyrowkKtHswajKG8=", "YtaosTXoxOlHtCj1ZpCzbQrzsSvQIRJHYUNq2xxYEYo=", "w5T4+OrYSo9WCWUyRLXtSz2LsEDCG0AaB31nsM3k2Vs=", "TgfS0tHmHVG3Y8ESKBcUBiuVJ6OWc/uE5t66wLZ6tI0=", "DkCMMtJBTVHdGeYOgghe5Oewi79iDPohSnUn7SmsQMw=", "4Rdqpb2cqqMmZQ9XC3j5DeLqTiWww274yXUUqSzMano=", "IdHlZdlswSCzXzlyqaFzzT5EaKQjtnlCP5dcX3HQ5pA=", "QX0iv2hZM2t3GrI0hzuB1DofwAktYONjhXWvniXu60c=", "1sHrgeI0hCbr5QnJyjQhBxRkSVXoziHT4LGnZEGLzQU=", "GNYB6XBEJA/ilGUMJ8xVOtNiwIYCMyyye9so2NDqy8U=", "m+sQCjnnkjtFEvQR1uc4TJ92ZpF9QqGLLjpnryoxEAI=", "AYkyGsSbtETUcU0wn5GsPNLOM/ZNP38lLGAL46U3N6M=", "zMGL6b/NBlaYQzQVfP5ZXpZW7wgUJrZA7S6oN+kjaio=", "Y6MRFMRALzJaqFElWrOhltMdpWUYe8v6jfAmHycO4GE=", "pi/anuU5GMXHLu+QpD8dutBga9ol5qDso19R51lN1Lc=", "UkC+/3aUnGu81MdMiQ3YXsnZ973kcsyYJgus7n/ToXk=", "IVVG8hr1IKgmt49Z2bfvC5r4YE6VDux1WmSXXvqk9MY=", "X1V7DiEwDdMZownGaPw0ES8RJj5jvvgPZ7yr44Oany8=", "Nqs7QBzFnbXRLE0FVVjDPwVvhJ04eZhIUHR5a8GKSpg=", "8a8581Aro9NX41MHjXRmQZPNb93fSWj/WTLrOzGhM6o=", "bFoJv15CGfjahriP/MNzWBLvwr8RU+XKPsqwvefEfkE="], "CachedAssets": {"cytOH4XOCw88QmvZ3rZ9QzZlARLl1h3SC6w8xKQ8rVI=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\ofa40bqe71-b9sayid5wm.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "css/site.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\css\\site.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0suozzrugb", "Integrity": "0FC1RbVkLkwKhJ1oi+r2Hw4Tm9av6DyJeo95YVvfA3Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\css\\site.css", "FileLength": 318, "LastWriteTime": "2025-06-08T16:52:24.2215873+00:00"}, "pbXKV3BYn4T0Hr7Wa2RTCfntFfBT1EJFzb6827ENmbc=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\j9swiz3yzq-90yqlj465b.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "favicon.ico.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\favicon.ico", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fxo9vb9gbf", "Integrity": "cwHpDnBTabxDzOHXFPOawb7ZlL3eysr4s1HfAD/K3vA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\favicon.ico", "FileLength": 9541, "LastWriteTime": "2025-06-08T16:52:24.2285836+00:00"}, "D/+4B0W1nGyytL0nMRuFE9yRNhI0w0lQUDafpkEm4Ro=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\vltxs1u3vm-xtxxf3hu2r.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "js/site.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\js\\site.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rl5dcbfpcw", "Integrity": "Ydq7YvQUzhbo68waLZeQvZnPOvmTeQ+HyDqjkA1whsA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\js\\site.js", "FileLength": 189, "LastWriteTime": "2025-06-08T16:52:24.2371815+00:00"}, "mKbSeUxxIkKpPRpz91/MdXVQmhSaoqSXkDlpzJUQ9WY=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\14ipv7q33s-bqjiyaj88i.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yf1pcrzlgs", "Integrity": "jhvPrvWZn8BbeR49W+r+MLNcnTeFyaSXxry9n1ctwy4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 6745, "LastWriteTime": "2025-06-08T16:52:24.2401798+00:00"}, "eNJUA11cvoDo+SbhT3qMMjWe8Q/lMvK8Nq/mqdvfPwI=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\rh65p086id-c2jlpeoesf.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "goh6yfn9uv", "Integrity": "ALPf6qsZMu+Ui8l8jPJJF3MhTcq6uwrQhRWeWJL4ixU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 32794, "LastWriteTime": "2025-06-08T16:52:24.2871434+00:00"}, "Q6R/xxonomUtjOTsuYKsF+lo+iaVjpsbXfa1GGip838=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\eb27mqwgz2-erw9l3u2r3.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ff2i3b225l", "Integrity": "y2vSlIdcL+ImKFhcBMT5ujdAP1cyOZlHZK434Aiu0KA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 5969, "LastWriteTime": "2025-06-08T16:52:24.291141+00:00"}, "e7RoElni0hnzSPYGVLD6uYuZU4z2quhSdRdZt6lR8mo=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\9glsckmvxo-aexeepp0ev.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p2onhk81wj", "Integrity": "oRq8VZWOZX9mMbVVZBzw8rSxg8D8d6u0L0zM8MMIvaE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 13807, "LastWriteTime": "2025-06-08T16:52:24.2321496+00:00"}, "ATuiQeOSST4uy+Rgv9juelz6D+XVzLS85JOxVOJOGMw=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\hh8ihyobdx-d7shbmvgxk.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "td9xh3ux7u", "Integrity": "P5V7Xl3ceCLw6wDeOyAezE6gOa9re3B7gTUN/H/cDsY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 6749, "LastWriteTime": "2025-06-08T16:52:24.2331835+00:00"}, "VMgHcv6zzch11rZsnnVAF1pbsvkrt9MljnImyDO/p5A=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\bww8ud00yd-ausgxo2sd3.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vo1c50q1ou", "Integrity": "cWfRgogdfOCr54Ae/lxY515FP4TJyUwc4Ae6uejBPI0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 32793, "LastWriteTime": "2025-06-08T16:52:24.2381811+00:00"}, "NGfJxi8LQl4c26Go1UYNVVMt9EE2caeenATttfiIyh0=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\edcrak57d7-k8d9w2qqmf.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2yj3hsx47l", "Integrity": "ujhFfu7SIw4cL8FWI0ezmD29C7bGSesJvlEcySm5beY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 5971, "LastWriteTime": "2025-06-08T16:52:24.2411787+00:00"}, "Lzs8jQfPjeESos0++T/Nb3JJo3haCkiMHkATluJFV4E=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\sf8kf2lula-cosvhxvwiu.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "d7q4cn5biw", "Integrity": "V0pKRwbw4DysvYMPCNK3s+wSdDLvMWJFO+hKrptop7A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 13815, "LastWriteTime": "2025-06-08T16:52:24.2441772+00:00"}, "S+9Nml4mBePRWNNNCJuZS/xVdgvxaVsMtHq9/wEF/E0=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\3tzsqhslk9-ub07r2b239.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4tzbrq9c6f", "Integrity": "+EIaQ03ZHgfVopnrJFjz7ZgQSAO9GeMOK+bzccTIQyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 3380, "LastWriteTime": "2025-06-08T16:52:24.2451774+00:00"}, "9EWOHXGv3ZTm4AHt614+rcGvbOOoEZrSIaY7EwR0mxI=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\oxk5o6czdw-fvhpjtyr6v.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yedk7y2ovv", "Integrity": "FjCFey27wknG6tewZOhPfnDgvIw+sTBly7wTSXKSd8M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 25821, "LastWriteTime": "2025-06-08T16:52:24.2441772+00:00"}, "M7TglD3Eh68bC+QujmR11I4ghF7GTSompv1RfttUoZM=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\k8b25g0zwc-b7pk76d08c.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "r79n6nskqp", "Integrity": "Z9B/f6Ax/2JeBbNo3F1oaFvmOzRvs3yS0nnykt272Wc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 3213, "LastWriteTime": "2025-06-08T16:52:24.2451774+00:00"}, "+tCyegHcFgwz89CcrPfpDtogPohgrXSQb0QwCBI6+BQ=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\8axix7wldr-fsbi9cje9m.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2wjbbjit2u", "Integrity": "crByaO07mpJWuBndE5BbjmmKh3fj/Y0m8CmJDe+c3UQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 12587, "LastWriteTime": "2025-06-08T16:52:24.2471751+00:00"}, "CQUUkKfn/sTC/35J1AERDdnPMsF35W0QAsq0JT3LW1w=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\d5z9sfpxbi-rzd6atqjts.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ukcr8jbv0r", "Integrity": "bhwmNaLC/7dOUfZxpXsnreiNsp2lbllRXrZLXndYFgA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 3367, "LastWriteTime": "2025-06-08T16:52:24.249765+00:00"}, "MxmMakwub83ciNcjLuHsTmnEPNCslZsNlqm/gDZ0ubA=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\u8428c4e9i-ee0r1s7dh0.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bxpwye2e51", "Integrity": "wbodRxtgYaqKj+oSPNLIcKGMPziM41to/lFFylYjPBc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 25833, "LastWriteTime": "2025-06-08T16:52:24.2487295+00:00"}, "42gjOT0m3lcvsshKlRlV2+lZVb+5zwxK2AysCW+v4dU=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\tg53r17ghy-dxx9fxp4il.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cv8bd5rjwi", "Integrity": "qNJnRAEMymFdtmi8gvc2VbVtDDk/UjeSnp+VQ10+cl0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 3246, "LastWriteTime": "2025-06-08T16:52:24.2507638+00:00"}, "kl61azyY73xyaZIj2Kp5mYdZVcefqEBL7LZ/aXVq8+k=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\nlrl0f3xs3-jd9uben2k1.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "l17cqhtmgf", "Integrity": "V8PtmOQL2VKj+/TynmuZNdDDyU9qG1QJ7FFnxVcN6Y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 15054, "LastWriteTime": "2025-06-08T16:52:24.2215873+00:00"}, "rhpDVIiPliiESPpVdCjTDhf2/tuME+XDGZnVSM0y/qM=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\1fc4q00scq-khv3u5hwcm.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bub42mguo1", "Integrity": "8+5oxr92QYcYeV3zAk8RS4XmZo6Y5i3ZmbiJXj9KVQo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 11991, "LastWriteTime": "2025-06-08T16:52:24.2275841+00:00"}, "zyaFMlTE5o6l7Gkk3B3xhjlJ6OwT9OXXgUop0g1P7mk=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\8xykfy6qmd-r4e9w2rdcm.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mns2a2zywm", "Integrity": "kj9zDkFgjDpUKwWasvv6a42LMVfqKjl/Ji5Z5LCNoRE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 44123, "LastWriteTime": "2025-06-08T16:52:24.2265846+00:00"}, "9dbAOHKxe2iiG+NgN94dnyvV+ZZpCjTRvowQolv59bw=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\vsrbd71spn-lcd1t2u6c8.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "iqwesyid6h", "Integrity": "xp5LPZ0vlqmxQrG+KjPm7ijhhJ+gD7VeH35lOUwBTWM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 11063, "LastWriteTime": "2025-06-08T16:52:24.2285836+00:00"}, "mx+MhB2bYtVz7NvbqUqXim4oeoCldk5BAGFt56Ru41w=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\cynp17w084-c2oey78nd0.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "q71g5sacw1", "Integrity": "Jh/LtOdwAMNcT5vpbDEGsxe6Xv18LR1JqK4h/+hvs5g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 24341, "LastWriteTime": "2025-06-08T16:52:24.2743441+00:00"}, "Qrz5iNF6ncjMG2n4cEQODSXIshP9OFjiDSNFIjuX0IQ=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\7o8oyvng68-tdbxkamptv.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "c9nucnnawf", "Integrity": "QAnDcxiLhrclwEVeKtd/GREdZNbXO2rZP5agorcS5EM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 11933, "LastWriteTime": "2025-06-08T16:52:24.2763427+00:00"}, "sa9pSLP0VdxTIKkeOBd1hPe5LJUDUTv9XTDToHxJ9/U=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\h9vwtoirpy-j5mq2jizvt.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nd4sjw69va", "Integrity": "KxjxxNhsaUlb9m0XwKNiMkNh6OuNbjdGXY0bmR5CTyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 44095, "LastWriteTime": "2025-06-08T16:52:24.2351822+00:00"}, "kM6a0/ZvhhumhEv8DUCdltXOffLyqiknizJWQP7UoKo=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\o5k23vqhrk-06098lyss8.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1dromj56xs", "Integrity": "hXLxKxNQS6hkMWOc1Po5uxTK8ovzNg0xvRC9wMKOZiM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 11046, "LastWriteTime": "2025-06-08T16:52:24.2371815+00:00"}, "BFZfscgvfWaqSshWb7MZYTFfubKlYsYySefOMwW03bQ=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\iznc766avz-nvvlpmu67g.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "oaf6lwhfh1", "Integrity": "8BfOknNd4oMU2u3DUY0C/Sjhoh3NGtdqE8kxApMdM2w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 24293, "LastWriteTime": "2025-06-08T16:52:24.2421786+00:00"}, "iwtDpdTkZMZooLfcN3GLSsHjyqLBPOLXUzNyhGdlc4g=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\ns4583i71s-s35ty4nyc5.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jw1qs72mo9", "Integrity": "I0QuKxdK89NxyamT6EeIfl/MyifdDw+D8cUjkiXwoOU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 33251, "LastWriteTime": "2025-06-08T16:52:24.2481744+00:00"}, "1vR+/da9hcCOgZRZf+7Usx16nPgX5C09IsGetSwSRM4=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\47012z8l2l-pj5nd1wqec.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1jikaxfu3u", "Integrity": "M4d5aODk+LnhCUggc/Xb6RX+Jh4E7X4KN58JXJR757I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 115009, "LastWriteTime": "2025-06-08T16:52:24.2881426+00:00"}, "Q3bUxDsx/pC7pfm1kqWJ5ITjJIo9RG+KCVa7dyppyc4=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\4vzefxwp2m-46ein0sx1k.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9buzyrzsnb", "Integrity": "NWIxwejcHtJ5yvljTypwFQBimL4GY/TpmkwWCoiPk+o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 30963, "LastWriteTime": "2025-06-08T16:52:24.2931394+00:00"}, "lmZrn5b3j3gW7X7rYSLQvcwpeAq5ik2N+ebUeMcfU6Q=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\bh4v3mdph9-v0zj4ognzu.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "we7ylowkap", "Integrity": "+BDBQp6fX0jhehydJj3yEmXwPsq4ccmpRwJadVX8HUA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 91807, "LastWriteTime": "2025-06-08T16:52:24.3081143+00:00"}, "UGN/H4sbD3otN9eANvWNw5cVmGnNq+rtGcYtAqxASvE=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\l186vlgaag-37tfw0ft22.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qhts6dsckw", "Integrity": "Tl7d+IXzMoFjiGRivA39XpNjGWA6jMfITy87ywcah6c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 33101, "LastWriteTime": "2025-06-08T16:52:24.3177207+00:00"}, "zYPPeJOppzSD79W//0jFaIId8qVaqzaFl1MVlSfrI6c=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\rsc7qacj1u-hrwsygsryq.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hack831yxc", "Integrity": "xwBA3wRtW8i96gsexkmrLvL85Ad0ueCN6i7I23oFCMU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 114953, "LastWriteTime": "2025-06-08T16:52:24.3403044+00:00"}, "A9Pk9fHu1OslWAXl23cdqpOttiPtnYeicnQfLfzK5eU=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\z408qb6q7a-pk9g2wxc8p.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ih1ajc97pa", "Integrity": "Bhl6D0ngVAgx68NwXp2DEDO390PSrA5dlFHCQXY4WgM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 30986, "LastWriteTime": "2025-06-08T16:52:24.3443032+00:00"}, "2lGBc9CecGSTvK7Z8elhNQ87oXnJgyq/WWI9U2rLlV8=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\qfr28sqcy1-ft3s53vfgj.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "j9d5qn8h15", "Integrity": "mVddgYoZfee39UGvBjujrPfkX4g9o5fJQgtcRjDKhDc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 91702, "LastWriteTime": "2025-06-08T16:52:24.3579312+00:00"}, "gRWw/WFwEw4kF5VeVQqeuKbwJxm75ftMOBudjwHH+y4=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\0k1i85ntyh-6cfz1n2cew.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sh9sbpd29q", "Integrity": "8KHWfFCoPlSmLyTbXOoHUNBddvrRpRlyAbs4j5nKGKY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 44354, "LastWriteTime": "2025-06-08T16:52:24.3629283+00:00"}, "ofEunXHsBsh20P9oMuNPnWANtO95/2jofQTOWq2UwG8=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\xa379ftczi-6pdc2jztkx.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sgz3bfuucz", "Integrity": "tELcWYAIUkPDirIRIOTlom3Q4rdUDcA6PdYMCRE48xY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 92045, "LastWriteTime": "2025-06-08T16:52:23.899343+00:00"}, "UPIs5LZcGEsUkY9Yj8Bs3XBqFOfu+nxo0Zq9WQY7fGM=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\lr5hmrr0j7-493y06b0oq.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9t6fi9687k", "Integrity": "PUb7rj1jLHgIo7Hwm3lvukBcGKDry7n7W2fa1xrz+zY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 23984, "LastWriteTime": "2025-06-08T16:52:24.2245856+00:00"}, "5pFSwggwTDmhNyeQsWxkrMY982jZstzThXc/CAMQNq8=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\ivy2s9gwh5-iovd86k7lj.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "svb68clhd0", "Integrity": "WDZTOK9dQrex7lgPEZZ+JZhLPdAF5GijB8N4Mslft/0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 86956, "LastWriteTime": "2025-06-08T16:52:24.2265846+00:00"}, "tEswAaqGZn2vkLCJVW45pPf+AY1ZDsGe/gZ+EjQthmc=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\higufxz8op-vr1egmr9el.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jtj7j0yqni", "Integrity": "6QziFU3u5nXZAGW+7TwN4NhocqzFBknypP5iUK5YK8k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 28852, "LastWriteTime": "2025-06-08T16:52:24.2305827+00:00"}, "B43Ktv2UMdVnTUqqKxDnBN/yg/MLRDpW5DnKAUGFKiI=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\s3tglgz8hr-kbrnm935zg.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qmaqe8uvdz", "Integrity": "UkbVh5EDjQ9ElFF2VidPUKfIufgj0++IuUkiaieELZw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 64130, "LastWriteTime": "2025-06-08T16:52:24.2321496+00:00"}, "I9PAmvpQ8QRDI2IqZWwidpx5ft+2i2tlQlM/seKKcXE=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\vw9sls9bhj-jj8uyg4cgr.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "erkthljg5u", "Integrity": "WmNnoBgejwchZUYVA3o03QOAGOuMRS778DeXvhP6suo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 18635, "LastWriteTime": "2025-06-08T16:52:24.2351822+00:00"}, "ZJF9Rl8WmjKIajXqsabhQiuPRGEbvvibFwxtpAwpemc=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\ym8tkpcl2i-y7v9cxd14o.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "klzs96wner", "Integrity": "OOTujdl0QaxckSfKf4pISOdHdkWzUDKsaJmaS87CLzk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 56667, "LastWriteTime": "2025-06-08T16:52:24.2361814+00:00"}, "4vH/mvk3IUUZTovQgHpb+YhX5BxEzHtbzujgGRSgpJI=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\zzna4nrm5w-notf2xhcfb.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4d5rpgxe6z", "Integrity": "6NzYRu+d/0puyGm6UFw/dwD9409WZbUx18xgnT/wQoQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 29569, "LastWriteTime": "2025-06-08T16:52:24.2421786+00:00"}, "Rkm743oyFMN7GYtgWKeGfefnu26Km88hSvBC8hizk7g=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\igscs4x0yk-h1s4sie4z3.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "42umladzh7", "Integrity": "rG54EGAHotdOswEyoMsIu5DDixozuuoKxcO7w4hcEQA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 64423, "LastWriteTime": "2025-06-08T16:52:24.249765+00:00"}, "ZAEWG40iNeq6aQ64kHyrYsOE/PUDIfmgoBaq2nHTcFA=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\zap00c0tb5-63fj8s7r0e.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "on09t9pmch", "Integrity": "bIPgOT88ycSklHWxEzFQVPvNsgNbss3QQbyTqHho4PA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 16636, "LastWriteTime": "2025-06-08T16:52:24.2547624+00:00"}, "JqVSA4Mf7X4Rm3VzcalvR3nzPfK8I4XVddiJyE9joec=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\pogzkicjpz-0j3bgjxly4.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "n46fiwynw7", "Integrity": "OYFfBiMA8guQaokr7JUEKEquxVFRad17YsNbJdI0IKM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 55848, "LastWriteTime": "2025-06-08T16:52:24.2411787+00:00"}, "wV9RdeiZOiMs92ZOXLHmb5OtNIHP93UyXBnBCHwHkk4=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\j3hwsq15kh-47otxtyo56.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7mrxpa<PERSON><PERSON><PERSON>", "Integrity": "8WSd7G1SSJrTChp1H0sIwU3oPQXDsKXVF1KkXEF6LuI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "FileLength": 4651, "LastWriteTime": "2025-06-08T16:52:24.2421786+00:00"}, "PbEAW3qfWfKnrCs7RgUD0UtgBkNM8QljIKYGRgl7C0Q=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\q3naettu8c-4v8eqarkd7.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kw5m9o3hck", "Integrity": "ZECRGyeVrglmY0fxP9wVujqjjSoecu3G7roYlvaINus=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "FileLength": 2207, "LastWriteTime": "2025-06-08T16:52:24.2431779+00:00"}, "1BzlWh0mbvN/IRJkHQ3+G1ezjY7f60k6VehwBvKWbfo=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\ldxy2n3w6q-356vix0kms.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2yhmugv22u", "Integrity": "WpL0kbwDHONKwUu+fAC6nKQXtZOBY20Gsspcn336Iz4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "FileLength": 694, "LastWriteTime": "2025-06-08T16:52:24.2441772+00:00"}, "VBXitRYxCnGFHziPfNkH91+9TBmVVcyvwLun8TgA848=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\ypthv7q62w-83jwlth58m.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/dist/additional-methods.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\additional-methods.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8j9rvpuvlg", "Integrity": "BIw6qCD4ysNHqPUgCkXJ/h+XlQ/h+2MuYEbvs/ZN+kM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\additional-methods.js", "FileLength": 14078, "LastWriteTime": "2025-06-08T16:52:24.2471751+00:00"}, "XGgCPNxMoFG7oe1IFDw2jhMlPedz5Oatsehe1sDUbxc=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\t6e3b82hrf-mrlpezrjn3.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/dist/additional-methods.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\additional-methods.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uya4g6am6w", "Integrity": "hhhjkgaWyJk2NWrF/LyhTIDOF42U+kBX72IU2K4RWJg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\additional-methods.min.js", "FileLength": 6482, "LastWriteTime": "2025-06-08T16:52:24.2487295+00:00"}, "/NpNAKcmKKUAep7crbT4zSOXW7JORIasI0LpbwZbeVc=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\uocis9wxbx-lzl9nlhx6b.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/dist/jquery.validate.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\jquery.validate.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "anlp8nykfw", "Integrity": "KuJPTvhuArDs4mj2zziNxM+hBWFbOhi0BKQgeR1pHG8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\jquery.validate.js", "FileLength": 14068, "LastWriteTime": "2025-06-08T16:52:24.2517636+00:00"}, "vnCtfXTzl9vvqEDWQOQgNfvSxC8oR6Bac/W3w8sXuI8=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\2tikzqlmtu-ag7o75518u.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hgmochx07c", "Integrity": "T/jNUyeLYdKQ3zndN8zs507jIg6GnkENMxuBqc1eXs0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "FileLength": 8121, "LastWriteTime": "2025-06-08T16:52:24.2587601+00:00"}, "pBNOQNUk8jxcnKKg3PfcuDOG/Q+mL8E2NefaLOhP2Io=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\9muusbdg0a-x0q3zqp4vz.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/LICENSE.md.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation\\LICENSE.md", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1fp9w2itex", "Integrity": "7bAe2yT3obcUnzDyFQyuh4GI4iC/NGB4Bs4gZaY3Wj4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation\\LICENSE.md", "FileLength": 683, "LastWriteTime": "2025-06-08T16:52:23.8906725+00:00"}, "plCanDehMmJGMeuCDteRCc/zPHvdWQPlxbvr7Jj8SRc=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\tpsq5dibur-0i3buxo5is.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "slhp14mdwv", "Integrity": "YPYGuVoBNkA8pP/JjlyjSON9Q7Ej1gVEUkbHrQT3uJU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.js", "FileLength": 84431, "LastWriteTime": "2025-06-08T16:52:24.2275841+00:00"}, "1Pn8pFbUro+xL91NL4wzh/2qnt0reCKDXJ/yRamWTLc=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\t0qo7o574p-o1o13a6vjx.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4efnowp72v", "Integrity": "rJUWNDEom3xIYh3mH5HCPpyh/CGhP0YhaSsaX6IOiDk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.min.js", "FileLength": 30683, "LastWriteTime": "2025-06-08T16:52:24.2305827+00:00"}, "+qIlkYC4lAqcBUUNv/oZXbCfjCbNJtNYjIfzB3Kbx4c=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\b9lhb08218-ttgo8qnofa.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.min.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hq2hiirxit", "Integrity": "GfL7M76amKU+B+H1o4WVh7q13GpiC7L6scp3ODOz3ag=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.min.map", "FileLength": 54456, "LastWriteTime": "2025-06-08T16:52:24.2351822+00:00"}, "/khUTZCvMrK6s/7VUoUVN4YIhXsxbtH0+4rYO7QFyq4=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\a3ygkwa5zy-2z0ns9nrw6.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.slim.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xlmiz42j7j", "Integrity": "9bWYPof03vxzc0YRmjwb6berDC7SXtE8rlvXxhIlakE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.js", "FileLength": 68601, "LastWriteTime": "2025-06-08T16:52:24.2431779+00:00"}, "P2DVS3c4hG7X9oHa5LBYwQ1AyxqQCSeHVsSJUwAhbbY=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\nezrqnk58p-muycvpuwrr.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.slim.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "trtvcfsjjx", "Integrity": "h68a4ojlMeHZ2MJNcpHGvBgERdoUw7Qpxk6a/oPD5kQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.min.js", "FileLength": 24360, "LastWriteTime": "2025-06-08T16:52:24.2451774+00:00"}, "XLq0zVlGnWRV2REMRk5aQk/yGFnQh2dRIE2353zd3vY=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\v0zgfp0y55-87fc7y1x7t.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.slim.min.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p5ev1mo4sb", "Integrity": "bZLUSM9fRhNJKF4yFjKvv9iU/j2aC1lJtEqR9u5n6y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.min.map", "FileLength": 43123, "LastWriteTime": "2025-06-08T16:52:24.249765+00:00"}, "a44tOHjSfYWxSrslxrYAFYzqIQ8FloWWxi+TG7/H/wI=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\yfc7ypekbg-mlv21k5csn.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/LICENSE.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k2cjzt041l", "Integrity": "JKWeHUaANNuffeRatdc54UCb84RoxjEw/nTq8Mops8Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery\\LICENSE.txt", "FileLength": 682, "LastWriteTime": "2025-06-08T16:52:24.2507638+00:00"}, "Nqs7QBzFnbXRLE0FVVjDPwVvhJ04eZhIUHR5a8GKSpg=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\8ngr7uwcar-8p2v1jy6gr.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "css/admin#[.{fingerprint=8p2v1jy6gr}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\css\\admin.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "tbp7e09e3z", "Integrity": "+Vmq1e1kTDdVqFlNBPdXHwgi6MgXVDsD/W8LWwG2bgM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\css\\admin.css", "FileLength": 7642, "LastWriteTime": "2025-06-08T16:58:04.4243051+00:00"}, "7nxbKsuMLIAVqPbNrkRXOJavUHK8FFeCCzRjdKMexiw=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\uh17ao849l-c5d7nsv465.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "css/admin/data-upload#[.{fingerprint=c5d7nsv465}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\css\\admin\\data-upload.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k4u8sr8gh0", "Integrity": "hLJQGm5k7msPwiAoUXAP033O2U9NMkQ2Onb5OJkqNJQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\css\\admin\\data-upload.css", "FileLength": 1786, "LastWriteTime": "2025-06-08T16:52:24.2567614+00:00"}, "8XsC6R7RHd8zQQ++WL+usGSrw8H6pdAuVlq0KLKymTQ=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\vzqep69h03-ttlnujb3md.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "css/chatbot#[.{fingerprint=ttlnujb3md}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\css\\chatbot.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "i3oc660vng", "Integrity": "Ap0MJU654qvh5mJKl0/CazXUcGLSMDuvMETk+sIdhVo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\css\\chatbot.css", "FileLength": 2529, "LastWriteTime": "2025-06-08T16:52:24.2587601+00:00"}, "/nwh7/thaJjT8q3l43CG0wlZaQ6/bvRtnDJbvk+ATVo=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\pn3zjfqylx-g07bnx142d.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "css/client#[.{fingerprint=g07bnx142d}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\css\\client.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "u372l94rk3", "Integrity": "hnZ3ayaGZFLAG6FjKMU6gjkmJLNka/tR9IAAQRpSPoc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\css\\client.css", "FileLength": 4346, "LastWriteTime": "2025-06-08T16:52:24.2607592+00:00"}, "rn40E6w3a+Na8VQ3GJoJPcxfRfRhyZPc/BaJu5nknYU=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\elvx5emtjm-wilnnc3w1m.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "css/custom#[.{fingerprint=wilnnc3w1m}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\css\\custom.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k58f1us78f", "Integrity": "CDABZ8Got8gWH2Wic8T6MC1izVu156P7c25rWmLiqLI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\css\\custom.css", "FileLength": 212, "LastWriteTime": "2025-06-08T16:52:24.2617584+00:00"}, "UznxwcqKqXsHXTtyae4VZcoeE1FrpAuBf7HuWdaVaZ0=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\ypbfssc6px-1edbnb0gu6.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "css/hero-slideshow#[.{fingerprint=1edbnb0gu6}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\css\\hero-slideshow.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kg1721j3l3", "Integrity": "3CYDHawx/sbpzjIQoN7+wnjWSSXtjNUj7txCQ3kydJI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\css\\hero-slideshow.css", "FileLength": 1718, "LastWriteTime": "2025-06-08T16:52:24.2617584+00:00"}, "wExBWOOxrg5t9aEXf1ZzQMWjkMRv51oOMFs8wMbt4Bo=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\xtt04yhxc6-0q4my8jvfl.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "css/modern-homepage#[.{fingerprint=0q4my8jvfl}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\css\\modern-homepage.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rcvxhwxgzm", "Integrity": "BaKfeGlWkAeD/OI5bepfDPACZjrHdgAWyxNU1bDp3LE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\css\\modern-homepage.css", "FileLength": 14185, "LastWriteTime": "2025-06-08T16:52:24.2647571+00:00"}, "NNMmpM8eBmKQPHLhkd77xIl4KalDW6jHhgD04kwynAE=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\7ejlrje5jg-8znpj9knio.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "css/site#[.{fingerprint=8znpj9knio}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\css\\site.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "tb45argtbf", "Integrity": "KXsoPJcXUP9jdH3J4w7jvn/bvOC4Ia8KvN2KNFAyres=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\css\\site.css", "FileLength": 2281, "LastWriteTime": "2025-06-08T16:52:24.2527636+00:00"}, "33l1R+EHUcv5APa2LHJLcbte4Os+SUwSILcHJwKTETM=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\03l1rte6gq-61n19gt1b8.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "favicon#[.{fingerprint=61n19gt1b8}]?.ico.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\favicon.ico", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nezsohjtde", "Integrity": "wfFuuYm+Lh6RbCeeiUqxw334b/hIOkp5j9eokGUM1Y0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\favicon.ico", "FileLength": 2468, "LastWriteTime": "2025-06-08T16:52:24.2577615+00:00"}, "nK6IIHAJif/Z8yA/HqdtOxK4G2kc97YMYzOgGtlbGbY=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\ktm00ucnju-wgrwvlfr5s.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/technologies/delphi#[.{fingerprint=wgrwvlfr5s}]?.svg.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\images\\technologies\\delphi.svg", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "tkbigiuy64", "Integrity": "HQM1ysqWHmUnuAlp3aK0g+H3KTdRrGOY3H0Vq22/RWA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\images\\technologies\\delphi.svg", "FileLength": 1935, "LastWriteTime": "2025-06-08T16:52:24.259759+00:00"}, "7UX9aJsexFoTdCXBuqw2gbj68WiaqD+78a+150ypxsA=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\qveyjng0ys-nedvdbx254.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/technologies/haskell#[.{fingerprint=nedvdbx254}]?.svg.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\images\\technologies\\haskell.svg", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "edsoh1itdo", "Integrity": "olpJlaPKyLgPjxsEUdPEwIwMpvYmE5KFaI68j+OJmJg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\images\\technologies\\haskell.svg", "FileLength": 336, "LastWriteTime": "2025-06-08T16:52:24.2185888+00:00"}, "AAn7eMsqd/LNifcl2scpGtdZOzLjwK5PF/oMXa3vlmE=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\ikbcgss9f7-k8cq9l2kib.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/technologies/nodejs#[.{fingerprint=k8cq9l2kib}]?.svg.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\images\\technologies\\nodejs.svg", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "lpoc1bcrbp", "Integrity": "GTXsRzccKflDEkzr6cT5Iq+5KyyN46VgzOgbjaIisWo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\images\\technologies\\nodejs.svg", "FileLength": 721, "LastWriteTime": "2025-06-08T16:52:24.220588+00:00"}, "opB5dL33tt86V6637fzKZmN2o3UZTF06a+QcWLx5ON0=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\nnketcjwya-1x8wi64s97.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/technologies/python#[.{fingerprint=1x8wi64s97}]?.svg.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\images\\technologies\\python.svg", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "j6yyh4b7nm", "Integrity": "i5K/+SeOT5dWe0FWBa0GIm1JLWXKxGhZVNxsYkLDrns=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\images\\technologies\\python.svg", "FileLength": 734, "LastWriteTime": "2025-06-08T16:52:24.2215873+00:00"}, "Gbgum7kXLfXdV5z+cj7/dySsVFUtc4CwyQ6J75WC0h0=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\uham8ign5j-emsbkume29.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/technologies/swift#[.{fingerprint=emsbkume29}]?.svg.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\images\\technologies\\swift.svg", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rvaavaxrke", "Integrity": "afO1CiL+LYXrmSV0hG/nzRY58iy/trDc+maeMmc1q8Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\images\\technologies\\swift.svg", "FileLength": 619, "LastWriteTime": "2025-06-08T16:52:24.2225867+00:00"}, "3WIw4uPpvTmaS1d3oIfOsx+hNGOsdXar/UeYWGnDSBM=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\10owx4zk9i-y36mr8d3re.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/technoloway-logo#[.{fingerprint=y36mr8d3re}]?.svg.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\images\\technoloway-logo.svg", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "78ry7ld12l", "Integrity": "qVZsciNX4bvJyG/NFAFG6/1bWRdPLgFZLpfMSEqLyZo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\images\\technoloway-logo.svg", "FileLength": 110221, "LastWriteTime": "2025-06-08T16:52:24.2361814+00:00"}, "BOZfW4WaRqtKMQxeMpERsAOXTXfo0fcso/3BTnmas+g=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\02bln8vcid-6de5fjlu8q.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "js/admin#[.{fingerprint=6de5fjlu8q}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\js\\admin.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bkj3u4c828", "Integrity": "y7OG3+AqATtB8KCnImHlz9DaMCW/SvkgUDW/ww3awkw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\js\\admin.js", "FileLength": 5688, "LastWriteTime": "2025-06-08T16:52:24.249765+00:00"}, "VWwTNGkIA66FiDnJIq6EeMDO5ox0eQZ4dNVP3QcydMg=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\v11a6fw95l-6e034c8k6q.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "js/admin/data-upload#[.{fingerprint=6e034c8k6q}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\js\\admin\\data-upload.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "n6g74qmyg4", "Integrity": "r1mwxzfPEyAu7eQsYwQEwWBoWRXu9vVzwbAF7+yWloU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\js\\admin\\data-upload.js", "FileLength": 4821, "LastWriteTime": "2025-06-08T16:52:24.2517636+00:00"}, "u4XVIG3gjh8J+BJhP2Pg9BLpVZp6RN3q4LbooS1o41Y=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\wb4hq6364b-ulg6rxb1b8.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "js/chatbot#[.{fingerprint=ulg6rxb1b8}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\js\\chatbot.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "17ine8nxd9", "Integrity": "3Vn+bM4vca8/ukjw9Mqrls1a+eAN6e822dXlFn01kzI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\js\\chatbot.js", "FileLength": 4496, "LastWriteTime": "2025-06-08T16:52:24.2537628+00:00"}, "gwSH2Au48FBsNuSu1t6vLnVYxHL3N5GuTi2pd9Tfukg=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\v8u4sxh3fr-3xu3gfddrx.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "js/client#[.{fingerprint=3xu3gfddrx}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\js\\client.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kkcwqz3pwi", "Integrity": "pD2v2NXmu8dIQdlAvDxSQC28LtmTLNKzC22UlumYprU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\js\\client.js", "FileLength": 390, "LastWriteTime": "2025-06-08T16:52:24.2567614+00:00"}, "qU+YfI17rcIlTN8GzGfdiqqwxX7gX4BpHhAvKSNP1nk=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\h4d7zuu3lf-rd81zyuzir.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "js/hero-slideshow#[.{fingerprint=rd81zyuzir}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\js\\hero-slideshow.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "6lq72lcfcg", "Integrity": "pkfBPs6ownzjPebnXD0fcxTJYLLrd3SJgTGnCXXCxXo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\js\\hero-slideshow.js", "FileLength": 1678, "LastWriteTime": "2025-06-08T16:52:24.2587601+00:00"}, "ntFKz4RFoavLLCPkjliEs3XsAbx7qTdKPXiDiAvjg5Q=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\br1wthuj05-wi7ss39h6q.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "js/homepage-animations#[.{fingerprint=wi7ss39h6q}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\js\\homepage-animations.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "iftq384gbj", "Integrity": "gqWjPa9AQ/+yPWfZCbXLhtYUgjFs8Oxn+qn1YXdxUNA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\js\\homepage-animations.js", "FileLength": 2761, "LastWriteTime": "2025-06-08T16:52:24.2607592+00:00"}, "PDImZ5WmEdH9YputCjmgYLvUyDoomCFTK9b1grocy6c=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\5eaaj8jli5-2svxw61t86.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "js/site#[.{fingerprint=2svxw61t86}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\js\\site.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "o75gd3adie", "Integrity": "ZHcCB5qku88L0iD5sq5euDqrhPm9N+4MeMk/tRrrgIg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\js\\site.js", "FileLength": 769, "LastWriteTime": "2025-06-08T16:52:24.2617584+00:00"}, "T5uUg7WhmFw6kMdEAwMbgoANEOrYg4S9rWa8KlYU7fc=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\huncoxf4nc-bqjiyaj88i.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint=bqjiyaj88i}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yf1pcrzlgs", "Integrity": "jhvPrvWZn8BbeR49W+r+MLNcnTeFyaSXxry9n1ctwy4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 6745, "LastWriteTime": "2025-06-08T16:52:24.2637576+00:00"}, "yQ+x+81YhVdS5tax5t2dLOopJbaNdV521XR0WlqkhyY=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\2jnr46nmpc-c2jlpeoesf.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint=c2jlpeoesf}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "goh6yfn9uv", "Integrity": "ALPf6qsZMu+Ui8l8jPJJF3MhTcq6uwrQhRWeWJL4ixU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 32794, "LastWriteTime": "2025-06-08T16:52:24.2683469+00:00"}, "Hz8w884iaaiy9Z9yQ/NbUCZbCwYUpsGnNt6AAAKwHKI=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\wmzyo7xuhb-erw9l3u2r3.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint=erw9l3u2r3}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ff2i3b225l", "Integrity": "y2vSlIdcL+ImKFhcBMT5ujdAP1cyOZlHZK434Aiu0KA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 5969, "LastWriteTime": "2025-06-08T16:52:24.2713464+00:00"}, "/bk67e2OOFNnGx684kcwfWYRSw8E2kxTP/107cbZsX8=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\djyxqsu5pv-aexeepp0ev.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint=aexeepp0ev}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p2onhk81wj", "Integrity": "oRq8VZWOZX9mMbVVZBzw8rSxg8D8d6u0L0zM8MMIvaE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 13807, "LastWriteTime": "2025-06-08T16:52:24.2743441+00:00"}, "+XyPqLyLYIchyZ+vdPnw/rR+PQxzqmOpqmQWL5jWMfE=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\2sbuxwf498-d7shbmvgxk.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint=d7shbmvgxk}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "td9xh3ux7u", "Integrity": "P5V7Xl3ceCLw6wDeOyAezE6gOa9re3B7gTUN/H/cDsY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 6749, "LastWriteTime": "2025-06-08T16:52:24.2941393+00:00"}, "PAjnCt3TW2WnAXz7wt1S3EHZqmMTZLj8feLf8iocQBw=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\fe676pkbci-ausgxo2sd3.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint=ausgxo2sd3}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vo1c50q1ou", "Integrity": "cWfRgogdfOCr54Ae/lxY515FP4TJyUwc4Ae6uejBPI0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 32793, "LastWriteTime": "2025-06-08T16:52:24.2986775+00:00"}, "PMiNlXHNQB8u9JwoHXMeLdT7BWX+0B6p31mcaA0jUpE=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\2n44t9y1oe-k8d9w2qqmf.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint=k8d9w2qqmf}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2yj3hsx47l", "Integrity": "ujhFfu7SIw4cL8FWI0ezmD29C7bGSesJvlEcySm5beY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 5971, "LastWriteTime": "2025-06-08T16:52:24.225586+00:00"}, "N8dyKqB4Ae5tNQ1pUh829C9FgnMDltoYTf5mEsClAjI=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\2yiqndbjob-cosvhxvwiu.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint=cosvhxvwiu}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "d7q4cn5biw", "Integrity": "V0pKRwbw4DysvYMPCNK3s+wSdDLvMWJFO+hKrptop7A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 13815, "LastWriteTime": "2025-06-08T16:52:24.2683469+00:00"}, "E06fGQW4Vq6bPJNHBUiWY09jyRVJpSxN/WF04gTzf0o=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\6laymexpp1-ub07r2b239.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint=ub07r2b239}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4tzbrq9c6f", "Integrity": "+EIaQ03ZHgfVopnrJFjz7ZgQSAO9GeMOK+bzccTIQyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 3380, "LastWriteTime": "2025-06-08T16:52:24.2713464+00:00"}, "Ox7B5wGlJ3EgU6umyvB6uu2h5JPXhAsutRNxHc+wTt8=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\e23n28kbl6-fvhpjtyr6v.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint=fvhpjtyr6v}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yedk7y2ovv", "Integrity": "FjCFey27wknG6tewZOhPfnDgvIw+sTBly7wTSXKSd8M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 25821, "LastWriteTime": "2025-06-08T16:52:24.259759+00:00"}, "ppfk9kvXAqKcNHDoE6IYKYMSn+QtUbZao1XX2pEya74=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\0xdmrg7j3x-b7pk76d08c.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint=b7pk76d08c}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "r79n6nskqp", "Integrity": "Z9B/f6Ax/2JeBbNo3F1oaFvmOzRvs3yS0nnykt272Wc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 3213, "LastWriteTime": "2025-06-08T16:52:24.2773425+00:00"}, "H4oLrQEqHtUHmv2T8cn5Q4wbv2rd5vCuBWKiOSxp8pU=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\7msow90u6m-fsbi9cje9m.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint=fsbi9cje9m}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2wjbbjit2u", "Integrity": "crByaO07mpJWuBndE5BbjmmKh3fj/Y0m8CmJDe+c3UQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 12587, "LastWriteTime": "2025-06-08T16:52:24.2861433+00:00"}, "WpJQE2rB62eUkbJlkdrPEjiPl+mQr+MvnqIEeJmVTVc=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\y6lzeo23io-rzd6atqjts.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint=rzd6atqjts}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ukcr8jbv0r", "Integrity": "bhwmNaLC/7dOUfZxpXsnreiNsp2lbllRXrZLXndYFgA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 3367, "LastWriteTime": "2025-06-08T16:52:24.2871434+00:00"}, "AbXE1HT4SUEU9/j6s3ceoSHGYvq4+62F7bT8pt2GfFg=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\b1eq4g63qz-ee0r1s7dh0.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint=ee0r1s7dh0}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bxpwye2e51", "Integrity": "wbodRxtgYaqKj+oSPNLIcKGMPziM41to/lFFylYjPBc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 25833, "LastWriteTime": "2025-06-08T16:52:24.2941393+00:00"}, "ltPeHrVPM10dRJRrF44Ia0khdmFOi9QpAajyXWmgA0k=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\q7xywl88yg-dxx9fxp4il.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint=dxx9fxp4il}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cv8bd5rjwi", "Integrity": "qNJnRAEMymFdtmi8gvc2VbVtDDk/UjeSnp+VQ10+cl0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 3246, "LastWriteTime": "2025-06-08T16:52:24.3041171+00:00"}, "U9OiU+vd7W+05D/LgXAH1GYTTwT6bDNSq12/0CIxNG4=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\tl4yfr4qca-jd9uben2k1.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint=jd9uben2k1}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "l17cqhtmgf", "Integrity": "V8PtmOQL2VKj+/TynmuZNdDDyU9qG1QJ7FFnxVcN6Y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 15054, "LastWriteTime": "2025-06-08T16:52:24.3061155+00:00"}, "fz/EiH0X7kf07aQLHnvKUBd5lJKfuLBjEcUWjnos81U=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\8xb1bhl2rn-khv3u5hwcm.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint=khv3u5hwcm}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bub42mguo1", "Integrity": "8+5oxr92QYcYeV3zAk8RS4XmZo6Y5i3ZmbiJXj9KVQo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 11991, "LastWriteTime": "2025-06-08T16:52:24.3121119+00:00"}, "LRH2iUaR6G1JcsWxW16/9kiuVJWJ3Cz1dvPgyO8yk3Q=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\1ju4p3k34w-r4e9w2rdcm.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint=r4e9w2rdcm}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mns2a2zywm", "Integrity": "kj9zDkFgjDpUKwWasvv6a42LMVfqKjl/Ji5Z5LCNoRE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 44123, "LastWriteTime": "2025-06-08T16:52:24.2753443+00:00"}, "FnWAMK9iX1pmHx6Kwe+uZnwClZRKbKL8r7m7mXZMGZ0=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\jyl4075mxi-lcd1t2u6c8.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint=lcd1t2u6c8}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "iqwesyid6h", "Integrity": "xp5LPZ0vlqmxQrG+KjPm7ijhhJ+gD7VeH35lOUwBTWM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 11063, "LastWriteTime": "2025-06-08T16:52:24.2841456+00:00"}, "nQdNtii15HRFT8JHDAMz3Bc+tEZ4j/wPYVsFBNHn4o8=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\zul1z3i66m-c2oey78nd0.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint=c2oey78nd0}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "q71g5sacw1", "Integrity": "Jh/LtOdwAMNcT5vpbDEGsxe6Xv18LR1JqK4h/+hvs5g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 24341, "LastWriteTime": "2025-06-08T16:52:24.2951384+00:00"}, "hoGvUD4Iu0dYsUxwUWySIV3fjBLxcZsPfxMSHqZNgnk=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\tj8j7uiu1e-tdbxkamptv.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint=tdbxkamptv}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "c9nucnnawf", "Integrity": "QAnDcxiLhrclwEVeKtd/GREdZNbXO2rZP5agorcS5EM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 11933, "LastWriteTime": "2025-06-08T16:52:24.3051161+00:00"}, "JRgRqsAf3mKXt8zFI/kOlz/Iz4pq7D8eOPuIaQUoN2s=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\l6ou9js1o2-j5mq2jizvt.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint=j5mq2jizvt}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nd4sjw69va", "Integrity": "KxjxxNhsaUlb9m0XwKNiMkNh6OuNbjdGXY0bmR5CTyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 44095, "LastWriteTime": "2025-06-08T16:52:24.3131113+00:00"}, "vUtnezYu9dSodCjWny3Zlj4ouMWaeKOAJMg2+/XtJZk=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\z3s3pqtpk8-06098lyss8.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint=06098lyss8}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1dromj56xs", "Integrity": "hXLxKxNQS6hkMWOc1Po5uxTK8ovzNg0xvRC9wMKOZiM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 11046, "LastWriteTime": "2025-06-08T16:52:24.3227185+00:00"}, "hd9bZV6HJSdWSNtxWlNnUX76knJ/oXlIm1fclagYXCg=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\pkmk90gfad-nvvlpmu67g.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint=nvvlpmu67g}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "oaf6lwhfh1", "Integrity": "8BfOknNd4oMU2u3DUY0C/Sjhoh3NGtdqE8kxApMdM2w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 24293, "LastWriteTime": "2025-06-08T16:52:24.328716+00:00"}, "aH3mwbwK3tou8DsB7geLB94Gh52MmKzWjfn+Nt7+6UE=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\zktux6y5y4-s35ty4nyc5.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint=s35ty4nyc5}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jw1qs72mo9", "Integrity": "I0QuKxdK89NxyamT6EeIfl/MyifdDw+D8cUjkiXwoOU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 33251, "LastWriteTime": "2025-06-08T16:52:24.2285836+00:00"}, "03Y1uYlpIF0dE5wlN7KTPIBZ8X8tP6mj3FMFuj9UpX8=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\26o4et715b-pj5nd1wqec.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint=pj5nd1wqec}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1jikaxfu3u", "Integrity": "M4d5aODk+LnhCUggc/Xb6RX+Jh4E7X4KN58JXJR757I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 115009, "LastWriteTime": "2025-06-08T16:52:24.2803402+00:00"}, "Vj+t6b2JPfXRr/KxmL9qBBocemK9hYpRlwwKC/lfGCo=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\7gp52xdync-46ein0sx1k.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint=46ein0sx1k}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9buzyrzsnb", "Integrity": "NWIxwejcHtJ5yvljTypwFQBimL4GY/TpmkwWCoiPk+o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 30963, "LastWriteTime": "2025-06-08T16:52:24.2931394+00:00"}, "9dgs0xu1QudaS5ZnB9EJcj+xRBjBZLV+c5WvVaPW9Po=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\e7b6u3x55w-v0zj4ognzu.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint=v0zj4ognzu}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "we7ylowkap", "Integrity": "+BDBQp6fX0jhehydJj3yEmXwPsq4ccmpRwJadVX8HUA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 91807, "LastWriteTime": "2025-06-08T16:52:24.2871434+00:00"}, "FiyyEyK1PxCpcVZU7pJyMAj0zI9SuQzl2mv1G9s4mro=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\9zg9c6chb7-37tfw0ft22.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint=37tfw0ft22}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qhts6dsckw", "Integrity": "Tl7d+IXzMoFjiGRivA39XpNjGWA6jMfITy87ywcah6c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 33101, "LastWriteTime": "2025-06-08T16:52:24.2981366+00:00"}, "usAnrxcrNp7pDsEmW5gjq0xfLlgE9RPeSolSYxDxj+E=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\33czbugvzg-hrwsygsryq.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint=hrwsygsryq}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hack831yxc", "Integrity": "xwBA3wRtW8i96gsexkmrLvL85Ad0ueCN6i7I23oFCMU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 114953, "LastWriteTime": "2025-06-08T16:52:24.3187201+00:00"}, "vuJCokmXD42cjDScmmbPbC9ClOOZzx5P7n+Kgad0hbE=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\2d5epgijjo-pk9g2wxc8p.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint=pk9g2wxc8p}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ih1ajc97pa", "Integrity": "Bhl6D0ngVAgx68NwXp2DEDO390PSrA5dlFHCQXY4WgM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 30986, "LastWriteTime": "2025-06-08T16:52:24.3333087+00:00"}, "pe/cDNF1WDWScH7gJcyYOvlQtTqbSdd4V7699vVB5y4=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\t63suxyd54-ft3s53vfgj.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint=ft3s53vfgj}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "j9d5qn8h15", "Integrity": "mVddgYoZfee39UGvBjujrPfkX4g9o5fJQgtcRjDKhDc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 91702, "LastWriteTime": "2025-06-08T16:52:24.3463013+00:00"}, "9LEwbr4tI/kVHXRlPXLZnHXGABLOV0na/YelRox3Fr4=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\w5r96tufml-6cfz1n2cew.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint=6cfz1n2cew}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sh9sbpd29q", "Integrity": "8KHWfFCoPlSmLyTbXOoHUNBddvrRpRlyAbs4j5nKGKY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 44354, "LastWriteTime": "2025-06-08T16:52:24.352933+00:00"}, "dhsivAhwOYqqBZZZB+UFgscHtGRNzPPHxdfSKgaFM4Q=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\wegvxza2ge-6pdc2jztkx.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint=6pdc2jztkx}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sgz3bfuucz", "Integrity": "tELcWYAIUkPDirIRIOTlom3Q4rdUDcA6PdYMCRE48xY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 92045, "LastWriteTime": "2025-06-08T16:52:24.3639281+00:00"}, "QN29E3Jqs88jSARuG55Uuj+aobRbex8rEY5w9Ol9HmU=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\g09mkto0sq-493y06b0oq.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint=493y06b0oq}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9t6fi9687k", "Integrity": "PUb7rj1jLHgIo7Hwm3lvukBcGKDry7n7W2fa1xrz+zY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 23984, "LastWriteTime": "2025-06-08T16:52:24.3666335+00:00"}, "2QS/cEzFHI2H2Rrmz6oOdQ8m2IHKKhEu4abetH/ozoI=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\4mg52w1lo3-iovd86k7lj.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint=iovd86k7lj}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "svb68clhd0", "Integrity": "WDZTOK9dQrex7lgPEZZ+JZhLPdAF5GijB8N4Mslft/0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 86956, "LastWriteTime": "2025-06-08T16:52:24.3729643+00:00"}, "3osGeOGKwLf8zkynplGJ3v/CUPcAr9Rak9Y3UjA5rRg=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\lh2isbcsad-vr1egmr9el.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint=vr1egmr9el}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jtj7j0yqni", "Integrity": "6QziFU3u5nXZAGW+7TwN4NhocqzFBknypP5iUK5YK8k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 28852, "LastWriteTime": "2025-06-08T16:52:24.3749739+00:00"}, "Q1w6o7sUNQ0sCKd29WO5JFg3v3da3TkHt5DXjnx6UlA=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\g1ftby5py3-kbrnm935zg.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint=kbrnm935zg}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qmaqe8uvdz", "Integrity": "UkbVh5EDjQ9ElFF2VidPUKfIufgj0++IuUkiaieELZw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 64130, "LastWriteTime": "2025-06-08T16:52:24.3832665+00:00"}, "E0MVCGJhEglNiMGXmHDapT7bGZgL5RJHoKKY+4vCTkQ=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\vphg83v0d2-jj8uyg4cgr.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint=jj8uyg4cgr}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "erkthljg5u", "Integrity": "WmNnoBgejwchZUYVA3o03QOAGOuMRS778DeXvhP6suo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 18635, "LastWriteTime": "2025-06-08T16:52:24.3845774+00:00"}, "dsfXAOpjcGT1wRwJkOTb3r59KMSn5h2QNCsT7nLTOI4=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\hd7xpsd4po-y7v9cxd14o.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint=y7v9cxd14o}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "klzs96wner", "Integrity": "OOTujdl0QaxckSfKf4pISOdHdkWzUDKsaJmaS87CLzk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 56667, "LastWriteTime": "2025-06-08T16:52:24.3901665+00:00"}, "B5oes6nAdG6qG5ufNDLwnIBvV6Q9SZOuorKPa3pHKtM=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\2626qsfhg1-notf2xhcfb.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint=notf2xhcfb}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4d5rpgxe6z", "Integrity": "6NzYRu+d/0puyGm6UFw/dwD9409WZbUx18xgnT/wQoQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 29569, "LastWriteTime": "2025-06-08T16:52:24.3931652+00:00"}, "+9izxIDeCJtml7RBOZNllIWIVDQfyrowkKtHswajKG8=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\u8c68735sf-h1s4sie4z3.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint=h1s4sie4z3}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "42umladzh7", "Integrity": "rG54EGAHotdOswEyoMsIu5DDixozuuoKxcO7w4hcEQA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 64423, "LastWriteTime": "2025-06-08T16:52:24.401895+00:00"}, "YtaosTXoxOlHtCj1ZpCzbQrzsSvQIRJHYUNq2xxYEYo=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\fij7bwzp8x-63fj8s7r0e.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint=63fj8s7r0e}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "on09t9pmch", "Integrity": "bIPgOT88ycSklHWxEzFQVPvNsgNbss3QQbyTqHho4PA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 16636, "LastWriteTime": "2025-06-08T16:52:24.2215873+00:00"}, "w5T4+OrYSo9WCWUyRLXtSz2LsEDCG0AaB31nsM3k2Vs=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\cji4aywtqv-0j3bgjxly4.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint=0j3bgjxly4}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "n46fiwynw7", "Integrity": "OYFfBiMA8guQaokr7JUEKEquxVFRad17YsNbJdI0IKM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 55848, "LastWriteTime": "2025-06-08T16:52:24.2851443+00:00"}, "TgfS0tHmHVG3Y8ESKBcUBiuVJ6OWc/uE5t66wLZ6tI0=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\9mvculopuj-47otxtyo56.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive#[.{fingerprint=47otxtyo56}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7mrxpa<PERSON><PERSON><PERSON>", "Integrity": "8WSd7G1SSJrTChp1H0sIwU3oPQXDsKXVF1KkXEF6LuI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "FileLength": 4651, "LastWriteTime": "2025-06-08T16:52:24.2871434+00:00"}, "DkCMMtJBTVHdGeYOgghe5Oewi79iDPohSnUn7SmsQMw=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\bf5okwooxo-4v8eqarkd7.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min#[.{fingerprint=4v8eqarkd7}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kw5m9o3hck", "Integrity": "ZECRGyeVrglmY0fxP9wVujqjjSoecu3G7roYlvaINus=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "FileLength": 2207, "LastWriteTime": "2025-06-08T16:52:24.2617584+00:00"}, "4Rdqpb2cqqMmZQ9XC3j5DeLqTiWww274yXUUqSzMano=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\g10371glbs-356vix0kms.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE#[.{fingerprint=356vix0kms}]?.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2yhmugv22u", "Integrity": "WpL0kbwDHONKwUu+fAC6nKQXtZOBY20Gsspcn336Iz4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "FileLength": 694, "LastWriteTime": "2025-06-08T16:52:24.262758+00:00"}, "IdHlZdlswSCzXzlyqaFzzT5EaKQjtnlCP5dcX3HQ5pA=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\2iu5gkm6iv-83jwlth58m.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/jquery-validation/dist/additional-methods#[.{fingerprint=83jwlth58m}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8j9rvpuvlg", "Integrity": "BIw6qCD4ysNHqPUgCkXJ/h+XlQ/h+2MuYEbvs/ZN+kM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "FileLength": 14078, "LastWriteTime": "2025-06-08T16:52:24.2647571+00:00"}, "QX0iv2hZM2t3GrI0hzuB1DofwAktYONjhXWvniXu60c=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\ahn74umtfx-mrlpezrjn3.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/jquery-validation/dist/additional-methods.min#[.{fingerprint=mrlpezrjn3}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uya4g6am6w", "Integrity": "hhhjkgaWyJk2NWrF/LyhTIDOF42U+kBX72IU2K4RWJg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "FileLength": 6482, "LastWriteTime": "2025-06-08T16:52:24.2653091+00:00"}, "1sHrgeI0hCbr5QnJyjQhBxRkSVXoziHT4LGnZEGLzQU=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\8k9meis7w4-lzl9nlhx6b.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/jquery-validation/dist/jquery.validate#[.{fingerprint=lzl9nlhx6b}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "anlp8nykfw", "Integrity": "KuJPTvhuArDs4mj2zziNxM+hBWFbOhi0BKQgeR1pHG8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "FileLength": 14068, "LastWriteTime": "2025-06-08T16:52:24.2683469+00:00"}, "GNYB6XBEJA/ilGUMJ8xVOtNiwIYCMyyye9so2NDqy8U=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\6jty019ari-ag7o75518u.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min#[.{fingerprint=ag7o75518u}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hgmochx07c", "Integrity": "T/jNUyeLYdKQ3zndN8zs507jIg6GnkENMxuBqc1eXs0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "FileLength": 8121, "LastWriteTime": "2025-06-08T16:52:24.2713464+00:00"}, "m+sQCjnnkjtFEvQR1uc4TJ92ZpF9QqGLLjpnryoxEAI=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\w5qk1pgd3r-x0q3zqp4vz.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/jquery-validation/LICENSE#[.{fingerprint=x0q3zqp4vz}]?.md.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1fp9w2itex", "Integrity": "7bAe2yT3obcUnzDyFQyuh4GI4iC/NGB4Bs4gZaY3Wj4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "FileLength": 683, "LastWriteTime": "2025-06-08T16:52:24.2723448+00:00"}, "AYkyGsSbtETUcU0wn5GsPNLOM/ZNP38lLGAL46U3N6M=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\64rjhqzavh-0i3buxo5is.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/jquery/dist/jquery#[.{fingerprint=0i3buxo5is}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery\\dist\\jquery.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "slhp14mdwv", "Integrity": "YPYGuVoBNkA8pP/JjlyjSON9Q7Ej1gVEUkbHrQT3uJU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery\\dist\\jquery.js", "FileLength": 84431, "LastWriteTime": "2025-06-08T16:52:24.2803402+00:00"}, "zMGL6b/NBlaYQzQVfP5ZXpZW7wgUJrZA7S6oN+kjaio=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\dmzx9f6yhi-o1o13a6vjx.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint=o1o13a6vjx}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4efnowp72v", "Integrity": "rJUWNDEom3xIYh3mH5HCPpyh/CGhP0YhaSsaX6IOiDk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "FileLength": 30683, "LastWriteTime": "2025-06-08T16:52:24.2831048+00:00"}, "Y6MRFMRALzJaqFElWrOhltMdpWUYe8v6jfAmHycO4GE=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\zydbwp8g7y-ttgo8qnofa.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint=ttgo8qnofa}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hq2hiirxit", "Integrity": "GfL7M76amKU+B+H1o4WVh7q13GpiC7L6scp3ODOz3ag=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "FileLength": 54456, "LastWriteTime": "2025-06-08T16:52:24.296138+00:00"}, "pi/anuU5GMXHLu+QpD8dutBga9ol5qDso19R51lN1Lc=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\tivk7iw1cd-2z0ns9nrw6.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/jquery/dist/jquery.slim#[.{fingerprint=2z0ns9nrw6}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xlmiz42j7j", "Integrity": "9bWYPof03vxzc0YRmjwb6berDC7SXtE8rlvXxhIlakE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "FileLength": 68601, "LastWriteTime": "2025-06-08T16:52:24.3121119+00:00"}, "UkC+/3aUnGu81MdMiQ3YXsnZ973kcsyYJgus7n/ToXk=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\2v4zxome5j-muycvpuwrr.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint=muycvpuwrr}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "trtvcfsjjx", "Integrity": "h68a4ojlMeHZ2MJNcpHGvBgERdoUw7Qpxk6a/oPD5kQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "FileLength": 24360, "LastWriteTime": "2025-06-08T16:52:24.3156774+00:00"}, "IVVG8hr1IKgmt49Z2bfvC5r4YE6VDux1WmSXXvqk9MY=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\e15jphxp1u-87fc7y1x7t.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint=87fc7y1x7t}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p5ev1mo4sb", "Integrity": "bZLUSM9fRhNJKF4yFjKvv9iU/j2aC1lJtEqR9u5n6y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "FileLength": 43123, "LastWriteTime": "2025-06-08T16:52:24.3297166+00:00"}, "X1V7DiEwDdMZownGaPw0ES8RJj5jvvgPZ7yr44Oany8=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\8ezk5r3swy-mlv21k5csn.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/jquery/LICENSE#[.{fingerprint=mlv21k5csn}]?.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k2cjzt041l", "Integrity": "JKWeHUaANNuffeRatdc54UCb84RoxjEw/nTq8Mops8Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery\\LICENSE.txt", "FileLength": 682, "LastWriteTime": "2025-06-08T16:52:24.3307146+00:00"}, "8a8581Aro9NX41MHjXRmQZPNb93fSWj/WTLrOzGhM6o=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\ot0u7hstz0-btisj24con.gz", "SourceId": "Technoloway.Web", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "Technoloway.Web#[.{fingerprint=btisj24con}]?.styles.css.gz", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\scopedcss\\bundle\\Technoloway.Web.styles.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "f0ib5o3ag5", "Integrity": "72RHN3eTubIZVjEZactNL1gtgMjQIrVfDuIfhwQDTvk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\scopedcss\\bundle\\Technoloway.Web.styles.css", "FileLength": 545, "LastWriteTime": "2025-06-08T16:52:24.3333087+00:00"}, "bFoJv15CGfjahriP/MNzWBLvwr8RU+XKPsqwvefEfkE=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\on9a315cdm-btisj24con.gz", "SourceId": "Technoloway.Web", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "Technoloway.Web#[.{fingerprint=btisj24con}]!.bundle.scp.css.gz", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\Technoloway.Web.bundle.scp.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "f0ib5o3ag5", "Integrity": "72RHN3eTubIZVjEZactNL1gtgMjQIrVfDuIfhwQDTvk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\Technoloway.Web.bundle.scp.css", "FileLength": 545, "LastWriteTime": "2025-06-08T16:52:24.2245856+00:00"}}, "CachedCopyCandidates": {}}