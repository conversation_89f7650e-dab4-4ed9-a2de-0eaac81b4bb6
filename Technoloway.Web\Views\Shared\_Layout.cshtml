﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - Technoloway</title>
    <meta name="description" content="@ViewData["MetaDescription"]" />
    <meta name="keywords" content="@ViewData["MetaKeywords"]" />
    <link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap.min.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
    <!-- Fallback for FontAwesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/custom.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/chatbot.css" asp-append-version="true" />

    @await RenderSectionAsync("Styles", required: false)
</head>
<body>
    <header>
        <nav class="navbar navbar-expand-lg navbar-modern">
            <div class="container">
                <a class="navbar-brand" asp-area="" asp-controller="Home" asp-action="Index">
                    <img src="~/images/technoloway-logo.svg" alt="Technoloway Logo" height="40" />
                </a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target=".navbar-collapse" aria-controls="navbarSupportedContent"
                        aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="navbar-collapse collapse d-lg-inline-flex justify-content-between">
                    <ul class="navbar-nav">
                        <li class="nav-item">
                            <a class="nav-link" asp-area="" asp-controller="Home" asp-action="Index">Home</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" asp-area="" asp-controller="Home" asp-action="About">About Us</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" asp-area="" asp-controller="Services" asp-action="Index">Services</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" asp-area="" asp-controller="Pricing" asp-action="Index">Pricing</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" asp-area="" asp-controller="Projects" asp-action="Index">Portfolio</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" asp-area="" asp-controller="Technologies" asp-action="Index">Technologies</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" asp-area="" asp-controller="Blog" asp-action="Index">Blog</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" asp-area="" asp-controller="Careers" asp-action="Index">Careers</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" asp-area="" asp-controller="Home" asp-action="Contact">Contact</a>
                        </li>
                    </ul>
                    <partial name="_LoginPartial" />
                </div>
            </div>
        </nav>
    </header>

    <main role="main">
        @RenderBody()
    </main>

    <footer class="bg-dark text-white py-5">
        <div class="container">
            <div class="row">
                @await Component.InvokeAsync("SocialLinks")
                @await Component.InvokeAsync("QuickLinks")
                @await Component.InvokeAsync("ContactInfo")
            </div>
            <hr class="my-4" />
            <div class="row">
                <div class="col-md-12 text-center">
                    <p class="mb-0">&copy; @DateTime.Now.Year - Technoloway - All Rights Reserved</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Inline script to immediately hide top corner buttons -->
    <script>
        // Immediately hide any top corner buttons
        (function() {
            var selectors = [
                '.top-corner-buttons',
                '[class*="top-corner"]',
                '[class*="corner-button"]',
                '.corner-buttons',
                'div[style*="position: absolute"][style*="top: 0"][style*="right: 0"]'
            ];

            selectors.forEach(function(selector) {
                var elements = document.querySelectorAll(selector);
                elements.forEach(function(element) {
                    element.style.display = 'none';
                    element.style.visibility = 'hidden';
                    element.remove();
                });
            });
        })();
    </script>

    <script src="~/lib/jquery/dist/jquery.min.js"></script>
    <script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>
    <script src="~/js/chatbot.js" asp-append-version="true"></script>
    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>
