// Pricing Page JavaScript
class PricingManager {
    constructor() {
        this.selectedCategory = null;
        this.selectedService = null;
        this.selectedOptions = [];
        this.selectedFeatures = [];
        this.currentTotal = 0;
        
        this.init();
    }

    init() {
        this.bindEvents();
    }

    bindEvents() {
        // Category customize buttons
        document.querySelectorAll('.customize-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const categoryId = e.target.dataset.categoryId;
                const categoryName = e.target.dataset.categoryName;
                this.openCustomization(categoryId, categoryName);
            });
        });
    }

    async openCustomization(categoryId, categoryName) {
        try {
            // Show loading
            this.showLoading();

            // Fetch category details
            const response = await fetch(`/Pricing/GetCategoryDetails?categoryId=${categoryId}`);
            const categoryData = await response.json();

            // Reset selections
            this.resetSelections();
            this.selectedCategory = categoryData;

            // Build customization UI
            this.buildCustomizationUI(categoryData);

            // Show modal
            const modal = new bootstrap.Modal(document.getElementById('customizationModal'));
            modal.show();

        } catch (error) {
            console.error('Error loading category details:', error);
            this.showError('Failed to load category details. Please try again.');
        } finally {
            this.hideLoading();
        }
    }

    buildCustomizationUI(category) {
        const content = document.getElementById('customizationContent');
        
        content.innerHTML = `
            <div class="container-fluid">
                <div class="row">
                    <div class="col-lg-8">
                        <!-- Step 1: Service Selection -->
                        <div class="customization-step">
                            <div class="step-header">
                                <div class="step-number">1</div>
                                <h4 class="step-title">Choose Your Service</h4>
                            </div>
                            <div class="service-grid">
                                ${this.buildServiceOptions(category.services)}
                            </div>
                        </div>

                        <!-- Step 2: Options Selection -->
                        <div class="customization-step" id="optionsStep" style="display: none;">
                            <div class="step-header">
                                <div class="step-number">2</div>
                                <h4 class="step-title">Select Options</h4>
                            </div>
                            <div id="optionsContainer">
                                <!-- Options will be loaded here -->
                            </div>
                        </div>

                        <!-- Step 3: Features Selection -->
                        <div class="customization-step" id="featuresStep" style="display: none;">
                            <div class="step-header">
                                <div class="step-number">3</div>
                                <h4 class="step-title">Add Features</h4>
                            </div>
                            <div id="featuresContainer">
                                <!-- Features will be loaded here -->
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-4">
                        <!-- Price Calculator -->
                        <div class="price-calculator">
                            <div class="calculator-header">
                                <h5 class="calculator-title">
                                    <i class="fas fa-calculator me-2"></i>Price Calculator
                                </h5>
                            </div>
                            <div class="price-breakdown" id="priceBreakdown">
                                <div class="price-line">
                                    <span>Base Service:</span>
                                    <span id="basePrice">$0</span>
                                </div>
                                <div class="price-line">
                                    <span>Options:</span>
                                    <span id="optionsPrice">$0</span>
                                </div>
                                <div class="price-line">
                                    <span>Features:</span>
                                    <span id="featuresPrice">$0</span>
                                </div>
                                <div class="price-line total">
                                    <span>Total:</span>
                                    <span class="total-amount" id="totalPrice">$0</span>
                                </div>
                            </div>
                            <button class="btn btn-success btn-lg w-100" id="requestQuoteBtn" disabled>
                                <i class="fas fa-file-invoice me-2"></i>Request Quote
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        this.bindCustomizationEvents();
    }

    buildServiceOptions(services) {
        return services.map(service => `
            <div class="service-option" data-service-id="${service.id}" data-service-price="${service.basePrice}">
                <div class="service-option-header">
                    <div class="service-icon">
                        <i class="${service.iconClass || 'fas fa-server'}"></i>
                    </div>
                    <div>
                        <h6 class="service-title">${service.name}</h6>
                        <div class="service-price">$${service.basePrice.toLocaleString()}</div>
                    </div>
                </div>
                <p class="service-description">${service.description}</p>
            </div>
        `).join('');
    }

    bindCustomizationEvents() {
        // Service selection
        document.querySelectorAll('.service-option').forEach(option => {
            option.addEventListener('click', (e) => {
                this.selectService(e.currentTarget);
            });
        });

        // Request quote button
        document.getElementById('requestQuoteBtn').addEventListener('click', () => {
            this.openQuotationForm();
        });
    }

    selectService(serviceElement) {
        // Remove previous selection
        document.querySelectorAll('.service-option').forEach(opt => {
            opt.classList.remove('selected');
        });

        // Add selection
        serviceElement.classList.add('selected');

        const serviceId = parseInt(serviceElement.dataset.serviceId);
        const servicePrice = parseFloat(serviceElement.dataset.servicePrice);

        // Find service data
        this.selectedService = this.selectedCategory.services.find(s => s.id === serviceId);

        // Update price
        this.updatePrice();

        // Show options step
        this.loadOptions();
        document.getElementById('optionsStep').style.display = 'block';

        // Enable quote button
        document.getElementById('requestQuoteBtn').disabled = false;
    }

    loadOptions() {
        const container = document.getElementById('optionsContainer');
        
        if (!this.selectedService.options || this.selectedService.options.length === 0) {
            container.innerHTML = '<p class="text-muted">No additional options available for this service.</p>';
            this.loadFeatures();
            return;
        }

        container.innerHTML = this.selectedService.options.map(option => `
            <div class="option-item d-flex align-items-center">
                <input type="checkbox" class="form-check-input option-checkbox" 
                       data-option-id="${option.id}" 
                       data-option-price="${option.cost || 0}"
                       data-option-name="${option.name}">
                <div class="option-info">
                    <div class="option-name">${option.name}</div>
                    ${option.description ? `<div class="option-description">${option.description}</div>` : ''}
                    <div class="option-price">+$${(option.cost || 0).toLocaleString()}</div>
                </div>
            </div>
        `).join('');

        // Bind option events
        container.querySelectorAll('.option-checkbox').forEach(checkbox => {
            checkbox.addEventListener('change', (e) => {
                this.toggleOption(e.target);
            });
        });

        this.loadFeatures();
    }

    loadFeatures() {
        const container = document.getElementById('featuresContainer');
        
        // Get all features from all options
        const allFeatures = this.selectedService.options.flatMap(option => option.features || []);
        
        if (allFeatures.length === 0) {
            container.innerHTML = '<p class="text-muted">No additional features available for this service.</p>';
            document.getElementById('featuresStep').style.display = 'block';
            return;
        }

        container.innerHTML = allFeatures.map(feature => `
            <div class="feature-item-modal d-flex align-items-center">
                <input type="checkbox" class="form-check-input feature-checkbox" 
                       data-feature-id="${feature.id}" 
                       data-feature-price="${feature.cost || 0}"
                       data-feature-name="${feature.name}">
                <div class="feature-info">
                    <div class="feature-name">${feature.name}</div>
                    ${feature.description ? `<div class="feature-description">${feature.description}</div>` : ''}
                    <div class="feature-price">+$${(feature.cost || 0).toLocaleString()}</div>
                </div>
            </div>
        `).join('');

        // Bind feature events
        container.querySelectorAll('.feature-checkbox').forEach(checkbox => {
            checkbox.addEventListener('change', (e) => {
                this.toggleFeature(e.target);
            });
        });

        document.getElementById('featuresStep').style.display = 'block';
    }

    toggleOption(checkbox) {
        const optionId = parseInt(checkbox.dataset.optionId);
        const optionPrice = parseFloat(checkbox.dataset.optionPrice);
        const optionName = checkbox.dataset.optionName;

        if (checkbox.checked) {
            this.selectedOptions.push({
                optionId: optionId,
                optionName: optionName,
                optionPrice: optionPrice
            });
        } else {
            this.selectedOptions = this.selectedOptions.filter(opt => opt.optionId !== optionId);
        }

        this.updatePrice();
    }

    toggleFeature(checkbox) {
        const featureId = parseInt(checkbox.dataset.featureId);
        const featurePrice = parseFloat(checkbox.dataset.featurePrice);
        const featureName = checkbox.dataset.featureName;

        if (checkbox.checked) {
            this.selectedFeatures.push({
                featureId: featureId,
                featureName: featureName,
                featurePrice: featurePrice
            });
        } else {
            this.selectedFeatures = this.selectedFeatures.filter(feat => feat.featureId !== featureId);
        }

        this.updatePrice();
    }

    updatePrice() {
        const basePrice = this.selectedService ? this.selectedService.basePrice : 0;
        const optionsPrice = this.selectedOptions.reduce((sum, opt) => sum + opt.optionPrice, 0);
        const featuresPrice = this.selectedFeatures.reduce((sum, feat) => sum + feat.featurePrice, 0);
        const total = basePrice + optionsPrice + featuresPrice;

        document.getElementById('basePrice').textContent = `$${basePrice.toLocaleString()}`;
        document.getElementById('optionsPrice').textContent = `$${optionsPrice.toLocaleString()}`;
        document.getElementById('featuresPrice').textContent = `$${featuresPrice.toLocaleString()}`;
        document.getElementById('totalPrice').textContent = `$${total.toLocaleString()}`;

        this.currentTotal = total;
    }

    resetSelections() {
        this.selectedCategory = null;
        this.selectedService = null;
        this.selectedOptions = [];
        this.selectedFeatures = [];
        this.currentTotal = 0;
    }

    showLoading() {
        // Implementation for loading state
    }

    hideLoading() {
        // Implementation for hiding loading state
    }

    showError(message) {
        alert(message); // Replace with better error handling
    }

    openQuotationForm() {
        if (!this.selectedService) {
            this.showError('Please select a service first.');
            return;
        }

        const quotationContent = document.getElementById('quotationContent');

        quotationContent.innerHTML = `
            <form id="quotationForm" class="quotation-form">
                <!-- Customer Information -->
                <div class="form-section">
                    <h6 class="section-title">Contact Information</h6>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="customerName" class="form-label">Full Name *</label>
                            <input type="text" class="form-control" id="customerName" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label">Email Address *</label>
                            <input type="email" class="form-control" id="email" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="phone" class="form-label">Phone Number</label>
                            <input type="tel" class="form-control" id="phone">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="company" class="form-label">Company</label>
                            <input type="text" class="form-control" id="company">
                        </div>
                    </div>
                </div>

                <!-- Quotation Summary -->
                <div class="form-section">
                    <h6 class="section-title">Quotation Summary</h6>
                    <div class="quotation-summary">
                        ${this.buildQuotationSummary()}
                    </div>
                </div>

                <!-- Additional Notes -->
                <div class="form-section">
                    <h6 class="section-title">Additional Requirements</h6>
                    <div class="mb-3">
                        <label for="additionalNotes" class="form-label">Tell us more about your project</label>
                        <textarea class="form-control" id="additionalNotes" rows="4"
                                  placeholder="Any specific requirements, timeline, or questions..."></textarea>
                    </div>
                </div>

                <!-- Submit Button -->
                <div class="d-grid">
                    <button type="submit" class="btn btn-success btn-lg">
                        <i class="fas fa-paper-plane me-2"></i>Submit Quotation Request
                    </button>
                </div>
            </form>
        `;

        // Bind form submission
        document.getElementById('quotationForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.submitQuotation();
        });

        // Show quotation modal
        const modal = new bootstrap.Modal(document.getElementById('quotationModal'));
        modal.show();
    }

    buildQuotationSummary() {
        return `
            <div class="summary-item">
                <div class="item-header">
                    <span class="item-name">${this.selectedCategory.name} - ${this.selectedService.name}</span>
                    <span class="item-price">$${this.selectedService.basePrice.toLocaleString()}</span>
                </div>
                <div class="item-details">${this.selectedService.description}</div>
            </div>

            ${this.selectedOptions.map(option => `
                <div class="summary-item">
                    <div class="item-header">
                        <span class="item-name">+ ${option.optionName}</span>
                        <span class="item-price">$${option.optionPrice.toLocaleString()}</span>
                    </div>
                </div>
            `).join('')}

            ${this.selectedFeatures.map(feature => `
                <div class="summary-item">
                    <div class="item-header">
                        <span class="item-name">+ ${feature.featureName}</span>
                        <span class="item-price">$${feature.featurePrice.toLocaleString()}</span>
                    </div>
                </div>
            `).join('')}

            <div class="summary-item">
                <div class="item-header">
                    <span class="item-name"><strong>Total Amount</strong></span>
                    <span class="item-price"><strong>$${this.currentTotal.toLocaleString()}</strong></span>
                </div>
            </div>
        `;
    }

    async submitQuotation() {
        try {
            // Show loading
            const submitBtn = document.querySelector('#quotationForm button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<span class="spinner me-2"></span>Submitting...';
            submitBtn.disabled = true;

            // Prepare quotation data
            const quotationData = {
                customerName: document.getElementById('customerName').value,
                email: document.getElementById('email').value,
                phone: document.getElementById('phone').value,
                company: document.getElementById('company').value,
                additionalNotes: document.getElementById('additionalNotes').value,
                totalAmount: this.currentTotal,
                items: [{
                    categoryId: this.selectedCategory.id,
                    categoryName: this.selectedCategory.name,
                    serviceId: this.selectedService.id,
                    serviceName: this.selectedService.name,
                    servicePrice: this.selectedService.basePrice,
                    selectedOptions: this.selectedOptions,
                    selectedFeatures: this.selectedFeatures,
                    itemTotal: this.currentTotal
                }]
            };

            // Submit quotation
            const response = await fetch('/Pricing/SubmitQuotation', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]')?.value
                },
                body: JSON.stringify(quotationData)
            });

            const result = await response.json();

            if (result.success) {
                this.showSuccessMessage(result.message, result.quotationId);
            } else {
                this.showError(result.message);
            }

        } catch (error) {
            console.error('Error submitting quotation:', error);
            this.showError('An error occurred while submitting your request. Please try again.');
        }
    }

    showSuccessMessage(message, quotationId) {
        const quotationContent = document.getElementById('quotationContent');
        quotationContent.innerHTML = `
            <div class="text-center py-5">
                <div class="success-message">
                    <i class="fas fa-check-circle success-icon"></i>
                    <h4 class="mb-3">Quotation Submitted Successfully!</h4>
                    <p class="mb-3">${message}</p>
                    <p><strong>Quotation ID:</strong> ${quotationId}</p>
                    <button class="btn btn-primary" onclick="location.reload()">
                        Create Another Quote
                    </button>
                </div>
            </div>
        `;
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new PricingManager();
});
