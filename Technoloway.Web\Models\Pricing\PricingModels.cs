using System.ComponentModel.DataAnnotations;

namespace Technoloway.Web.Models.Pricing;

public class CategoryPricingDto
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public decimal StartingPrice { get; set; }
    public string? IconClass { get; set; }
    public string? FeaturedFeature { get; set; }
    public int ServiceCount { get; set; }
    public List<ServicePricingDto> Services { get; set; } = new();
}

public class ServicePricingDto
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public decimal BasePrice { get; set; }
    public string? IconClass { get; set; }
    public bool IsActive { get; set; }
    public int DisplayOrder { get; set; }
    public List<ServiceOptionPricingDto> Options { get; set; } = new();
}

public class ServiceOptionPricingDto
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public decimal? Cost { get; set; }
    public int? DiscountRate { get; set; }
    public string? Availability { get; set; }
    public List<FeaturePricingDto> Features { get; set; } = new();
}

public class FeaturePricingDto
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public decimal? Cost { get; set; }
    public int? DiscountRate { get; set; }
    public string? Availability { get; set; }
}

public class QuotationRequestDto
{
    [Required]
    [StringLength(100)]
    public string CustomerName { get; set; } = string.Empty;

    [Required]
    [EmailAddress]
    [StringLength(255)]
    public string Email { get; set; } = string.Empty;

    [Phone]
    [StringLength(20)]
    public string? Phone { get; set; }

    [StringLength(100)]
    public string? Company { get; set; }

    [Required]
    public List<QuotationItemDto> Items { get; set; } = new();

    [StringLength(1000)]
    public string? AdditionalNotes { get; set; }

    public decimal TotalAmount { get; set; }
}

public class QuotationItemDto
{
    public int CategoryId { get; set; }
    public string CategoryName { get; set; } = string.Empty;
    public int ServiceId { get; set; }
    public string ServiceName { get; set; } = string.Empty;
    public decimal ServicePrice { get; set; }
    public List<SelectedOptionDto> SelectedOptions { get; set; } = new();
    public List<SelectedFeatureDto> SelectedFeatures { get; set; } = new();
    public decimal ItemTotal { get; set; }
}

public class SelectedOptionDto
{
    public int OptionId { get; set; }
    public string OptionName { get; set; } = string.Empty;
    public decimal OptionPrice { get; set; }
}

public class SelectedFeatureDto
{
    public int FeatureId { get; set; }
    public string FeatureName { get; set; } = string.Empty;
    public decimal FeaturePrice { get; set; }
}

public class QuotationResponseDto
{
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
    public string? QuotationId { get; set; }
}

public class PricingPageViewModel
{
    public List<CategoryPricingDto> Categories { get; set; } = new();
    public string PageTitle { get; set; } = "Our Services & Pricing";
    public string PageDescription { get; set; } = "Explore our comprehensive service offerings and get a custom quote tailored to your needs.";
}
