using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Technoloway.Core.Entities;
using Technoloway.Core.Interfaces;

namespace Technoloway.Web.Areas.Admin.Controllers;

[Area("Admin")]
[Authorize(Policy = "RequireAdminRole")]
public class QuotationsController : Controller
{
    private readonly IRepository<Quotation> _quotationRepository;
    private readonly IRepository<QuotationItem> _quotationItemRepository;

    public QuotationsController(
        IRepository<Quotation> quotationRepository,
        IRepository<QuotationItem> quotationItemRepository)
    {
        _quotationRepository = quotationRepository;
        _quotationItemRepository = quotationItemRepository;
    }

    public async Task<IActionResult> Index()
    {
        var quotations = await _quotationRepository.GetAll()
            .Include(q => q.Items)
            .OrderByDescending(q => q.CreatedAt)
            .ToListAsync();

        return View(quotations);
    }

    public async Task<IActionResult> Details(int id)
    {
        var quotation = await _quotationRepository.GetAll()
            .Include(q => q.Items)
            .FirstOrDefaultAsync(q => q.Id == id);

        if (quotation == null)
        {
            return NotFound();
        }

        return View(quotation);
    }

    [HttpPost]
    public async Task<IActionResult> UpdateStatus(int id, string status, string? adminNotes)
    {
        try
        {
            var quotation = await _quotationRepository.GetByIdAsync(id);
            if (quotation == null)
            {
                return Json(new { success = false, message = "Quotation not found." });
            }

            quotation.Status = status;
            quotation.AdminNotes = adminNotes;
            quotation.ReviewedAt = DateTime.UtcNow;
            quotation.ReviewedBy = User.Identity?.Name ?? "Admin";
            quotation.UpdatedAt = DateTime.UtcNow;

            await _quotationRepository.UpdateAsync(quotation);

            return Json(new { success = true, message = "Quotation status updated successfully." });
        }
        catch (Exception ex)
        {
            return Json(new { success = false, message = $"Error updating quotation: {ex.Message}" });
        }
    }

    [HttpPost]
    public async Task<IActionResult> Delete(int id)
    {
        try
        {
            var quotation = await _quotationRepository.GetByIdAsync(id);
            if (quotation == null)
            {
                return Json(new { success = false, message = "Quotation not found." });
            }

            quotation.IsDeleted = true;
            quotation.UpdatedAt = DateTime.UtcNow;
            await _quotationRepository.UpdateAsync(quotation);

            return Json(new { success = true, message = "Quotation deleted successfully." });
        }
        catch (Exception ex)
        {
            return Json(new { success = false, message = $"Error deleting quotation: {ex.Message}" });
        }
    }
}
