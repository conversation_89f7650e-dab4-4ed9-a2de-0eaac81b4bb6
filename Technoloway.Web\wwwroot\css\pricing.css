/* Pricing Page Styles */
:root {
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --warning-color: #ffc107;
    --info-color: #0dcaf0;
    --light-bg: #f8f9fa;
    --border-radius: 0.75rem;
    --shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    --shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);
    --transition: all 0.3s ease;
}

/* Pricing Section */
.pricing-section {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    min-height: 100vh;
}

/* Pricing Cards */
.pricing-card {
    transition: var(--transition);
    cursor: pointer;
}

.pricing-card:hover {
    transform: translateY(-10px);
}

.pricing-card-inner {
    border-radius: var(--border-radius);
    overflow: hidden;
    transition: var(--transition);
}

.pricing-card:hover .pricing-card-inner {
    box-shadow: var(--shadow-lg);
}

.pricing-icon {
    opacity: 0.9;
}

.price-display {
    position: relative;
}

.price-label {
    display: block;
    font-size: 0.875rem;
    margin-bottom: 0.5rem;
}

.price-amount {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
    line-height: 1;
}

.currency {
    font-size: 1.5rem;
    vertical-align: top;
}

.featured-feature {
    margin-top: 1rem;
}

.service-stats {
    border-top: 1px solid #dee2e6;
    padding-top: 1rem;
}

.stat-item {
    padding: 0.25rem 0;
}

/* Customize Button */
.customize-btn {
    border-radius: 50px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: var(--transition);
}

.customize-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 1rem rgba(13, 110, 253, 0.3);
}

/* Features Section */
.features-section {
    border: 1px solid #dee2e6;
}

.feature-item {
    padding: 1rem;
    transition: var(--transition);
}

.feature-item:hover {
    transform: translateY(-5px);
}

.feature-item i {
    transition: var(--transition);
}

.feature-item:hover i {
    transform: scale(1.1);
}

/* Customization Modal */
.customization-step {
    padding: 2rem;
    border-bottom: 1px solid #dee2e6;
}

.customization-step:last-child {
    border-bottom: none;
}

.step-header {
    display: flex;
    align-items: center;
    margin-bottom: 1.5rem;
}

.step-number {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    margin-right: 1rem;
}

.step-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0;
}

/* Service Selection */
.service-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.service-option {
    border: 2px solid #dee2e6;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    cursor: pointer;
    transition: var(--transition);
    background: white;
}

.service-option:hover {
    border-color: var(--primary-color);
    box-shadow: 0 0.25rem 0.5rem rgba(13, 110, 253, 0.1);
}

.service-option.selected {
    border-color: var(--primary-color);
    background: rgba(13, 110, 253, 0.05);
}

.service-option-header {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
}

.service-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: var(--light-bg);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    color: var(--primary-color);
}

.service-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0;
}

.service-price {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--success-color);
    margin-top: 0.5rem;
}

/* Options and Features */
.options-section, .features-section-modal {
    margin-top: 2rem;
}

.option-item, .feature-item-modal {
    border: 1px solid #dee2e6;
    border-radius: var(--border-radius);
    padding: 1rem;
    margin-bottom: 0.5rem;
    transition: var(--transition);
}

.option-item:hover, .feature-item-modal:hover {
    border-color: var(--primary-color);
}

.option-checkbox, .feature-checkbox {
    margin-right: 0.75rem;
}

.option-info, .feature-info {
    flex: 1;
}

.option-name, .feature-name {
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.option-description, .feature-description {
    font-size: 0.875rem;
    color: var(--secondary-color);
    margin-bottom: 0.5rem;
}

.option-price, .feature-price {
    font-weight: 600;
    color: var(--success-color);
}

/* Price Calculator */
.price-calculator {
    position: sticky;
    top: 2rem;
    background: white;
    border: 1px solid #dee2e6;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--shadow);
}

.calculator-header {
    border-bottom: 1px solid #dee2e6;
    padding-bottom: 1rem;
    margin-bottom: 1rem;
}

.calculator-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0;
}

.price-breakdown {
    margin-bottom: 1rem;
}

.price-line {
    display: flex;
    justify-content: space-between;
    padding: 0.25rem 0;
}

.price-line.total {
    border-top: 1px solid #dee2e6;
    padding-top: 0.75rem;
    margin-top: 0.75rem;
    font-weight: 700;
    font-size: 1.1rem;
}

.total-amount {
    color: var(--primary-color);
    font-size: 1.5rem;
}

/* Quotation Form */
.quotation-form {
    padding: 1.5rem;
}

.form-section {
    margin-bottom: 2rem;
}

.section-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--primary-color);
}

.quotation-summary {
    background: var(--light-bg);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.summary-item {
    border-bottom: 1px solid #dee2e6;
    padding: 1rem 0;
}

.summary-item:last-child {
    border-bottom: none;
}

.item-header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.item-name {
    font-weight: 600;
}

.item-price {
    font-weight: 600;
    color: var(--success-color);
}

.item-details {
    font-size: 0.875rem;
    color: var(--secondary-color);
}

/* Responsive Design */
@media (max-width: 768px) {
    .pricing-card {
        margin-bottom: 2rem;
    }
    
    .service-grid {
        grid-template-columns: 1fr;
    }
    
    .price-calculator {
        position: static;
        margin-top: 2rem;
    }
    
    .modal-xl {
        max-width: 95%;
    }
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.spinner {
    display: inline-block;
    width: 1rem;
    height: 1rem;
    border: 2px solid #f3f3f3;
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Success States */
.success-message {
    background: rgba(25, 135, 84, 0.1);
    border: 1px solid var(--success-color);
    border-radius: var(--border-radius);
    padding: 1rem;
    color: var(--success-color);
    text-align: center;
}

.success-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}
