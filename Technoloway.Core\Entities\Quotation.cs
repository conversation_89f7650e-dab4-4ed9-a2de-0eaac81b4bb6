using Technoloway.Core.Common;
using System.ComponentModel.DataAnnotations;

namespace Technoloway.Core.Entities;

public class Quotation : BaseEntity
{
    [Required]
    [StringLength(20)]
    public string QuotationNumber { get; set; } = string.Empty;

    [Required]
    [StringLength(100)]
    public string CustomerName { get; set; } = string.Empty;

    [Required]
    [EmailAddress]
    [StringLength(255)]
    public string Email { get; set; } = string.Empty;

    [Phone]
    [StringLength(20)]
    public string? Phone { get; set; }

    [StringLength(100)]
    public string? Company { get; set; }

    [Required]
    public decimal TotalAmount { get; set; }

    [StringLength(1000)]
    public string? AdditionalNotes { get; set; }

    [StringLength(20)]
    public string Status { get; set; } = "Pending"; // Pending, Reviewed, Approved, Rejected

    public DateTime? ReviewedAt { get; set; }
    public string? ReviewedBy { get; set; }
    public string? AdminNotes { get; set; }

    // Navigation properties
    public ICollection<QuotationItem> Items { get; set; } = new List<QuotationItem>();
}

public class QuotationItem : BaseEntity
{
    [Required]
    public int QuotationId { get; set; }

    [Required]
    public int CategoryId { get; set; }

    [Required]
    [StringLength(100)]
    public string CategoryName { get; set; } = string.Empty;

    [Required]
    public int ServiceId { get; set; }

    [Required]
    [StringLength(255)]
    public string ServiceName { get; set; } = string.Empty;

    [Required]
    public decimal ServicePrice { get; set; }

    [Required]
    public decimal ItemTotal { get; set; }

    public string? SelectedOptionsJson { get; set; } // JSON string of selected options
    public string? SelectedFeaturesJson { get; set; } // JSON string of selected features

    // Navigation properties
    public Quotation Quotation { get; set; } = null!;
}
